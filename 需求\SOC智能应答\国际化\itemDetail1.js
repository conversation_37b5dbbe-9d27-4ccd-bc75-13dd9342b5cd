export default {
  itemDetail: {
    // Page title
    pageTitle: 'Item Detail',
    backToTask: 'Back to Task',
    
    // Header info
    header: {
      itemDesc: 'Item Description',
      country: 'Country',
      groupCustomer: 'Group Customer/MTO',
      operator: 'Operator/Branch',
      customerInfo: 'Customer Info Supplement',
      productSwitch: 'Switch Product'
    },
    
    // Tab labels
    tabs: {
      responseResult: 'Response Result',
      matchDetails: 'Match Details'
    },
    
    // Response form
    responseForm: {
      historyVersion: 'History Version',
      plsSelectHistoryVersion: 'Please select history version',
      additionalInfo: 'Additional Info',
      satisfaction: 'Satisfaction',
      responseDescription: 'Response Description',
      source: 'Source',
      remark: 'Remark',
      plsEnterAdditionalInfo: 'Please enter additional info',
      plsSelectSatisfaction: 'Please select satisfaction',
      plsEnterResponseDescription: 'Please enter response description',
      plsEnterSource: 'Please enter source',
      plsEnterRemark: 'Please enter remark',
      save: 'Save',
      reset: 'Reset'
    },
    
    // Satisfaction options
    satisfaction: {
      fc: 'FC',
      pc: 'PC',
      nc: 'NC'
    },
    
    // Match details
    matchDetails: {
      // Statistics cards
      totalMatches: 'Total Matches',
      fcCount: 'FC',
      pcCount: 'PC',
      ncCount: 'NC',
      noMatchResults: 'No Match Results',
      // Query conditions
      queryConditions: 'Query Conditions',
      satisfactionFilter: 'Satisfaction',
      matchRateFilter: 'Match Rate',
      dataSourceFilter: 'Data Source',
      allSatisfaction: 'All',
      allMatchRate: 'All',
      matchRate90: '≥90%',
      matchRate80: '≥80%',
      matchRate70: '≥70%',
      gbbs: 'GBBS',
      
      // Match result cards
      matchRate: 'Match Rate',
      apply: 'Apply',
      itemDescription: 'Item Description',
      satisfaction: 'Satisfaction',
      responseDescription: 'Response Description',
      responseStrategy: 'Response Strategy',
      reference: 'Reference',
      applyConfirm: 'This will overwrite current response result, confirm to continue!'
    },
    
    // Messages
    msg: {
      loadFailed: 'Failed to load item detail',
      saveSuccess: 'Save successful',
      saveFailed: 'Save failed',
      resetSuccess: 'Reset successful',
      applySuccess: 'Apply successful',
      applyFailed: 'Apply failed',
      loadMatchResultsFailed: 'Failed to load match results'
    }
  }
};
