export default {
  itemDetail: {
    // 页面标题
    pageTitle: '条目详情',
    backToTask: '返回任务',
    
    // 头部信息
    header: {
      itemDesc: '条目描述',
      country: '国家',
      groupCustomer: '集团客户简称/MTO',
      operator: '运营商/分支',
      customerInfo: '客户信息补充',
      productSwitch: '切换产品'
    },
    
    // Tab标签
    tabs: {
      responseResult: '应答结果',
      matchDetails: '匹配详情'
    },
    
    // 应答结果表单
    responseForm: {
      historyVersion: '历史版本',
      plsSelectHistoryVersion: '请选择历史版本',
      additionalInfo: '补充信息',
      satisfaction: '满足度',
      responseDescription: '应答说明',
      source: '来源',
      remark: '备注',
      plsEnterAdditionalInfo: '请输入补充信息',
      plsSelectSatisfaction: '请选择应答',
      plsEnterResponseDescription: '请输入应答说明',
      plsEnterSource: '请输入来源',
      plsEnterRemark: '请输入备注',
      save: '保存',
      reset: '重置'
    },
    
    // 满足度选项
    satisfaction: {
      fc: 'FC',
      pc: 'PC', 
      nc: 'NC'
    },
    
    // 匹配详情
    matchDetails: {
      // 统计卡片
      totalMatches: '匹配结果总数',
      fcCount: 'FC',
      pcCount: 'PC',
      ncCount: 'NC',
      noMatchResults: '暂无匹配结果',
      
      // 查询条件
      queryConditions: '查询条件',
      satisfactionFilter: '满足度',
      matchRateFilter: '匹配度',
      dataSourceFilter: '数据源',
      allSatisfaction: '全部',
      allMatchRate: '全部',
      matchRate90: '≥90%',
      matchRate80: '≥80%',
      matchRate70: '≥70%',
      gbbs: 'GBBS',
      
      // 匹配结果卡片
      matchRate: '匹配度',
      apply: '应用',
      itemDescription: '条目描述',
      satisfaction: '满足度',
      responseDescription: '应答说明',
      responseStrategy: '应答策略',
      reference: '索引',
      applyConfirm: '将覆盖当前应答结果，请确认是否继续！',

    },
    
    // 消息提示
    msg: {
      loadFailed: '加载条目详情失败',
      saveSuccess: '保存成功',
      saveFailed: '保存失败',
      resetSuccess: '重置成功',
      applySuccess: '应用成功',
      applyFailed: '应用失败',
      loadMatchResultsFailed: '加载匹配结果失败'
    }
  }
};
