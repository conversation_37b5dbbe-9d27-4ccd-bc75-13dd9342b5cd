---
type: "manual"
---

description:
globs:
alwaysApply: false 

任务目标
作为产品经理，我需要基于需求方案及功能点生成标准化的产品需求文档，为开发团队提供明确、完整、可执行的实现指导。 
工作原则
深度理解
：深入分析功能背景、用户场景、业务价值和约束条件 
结构化清晰表达
：按标准模板逐章节完成，确保文档完整性和一致性，表达清晰无歧义，确保人和AI都能理解 
细节把控
：每个功能点都要包含完整的业务逻辑、数据设计、异常处理 
可执行性
：提供明确的开发指导，包括数据表设计、接口要求、验收标准

要求
禁止任何形式的杜撰、推测或补充未提及的内容
：严格以我提供的原始材料为信息来源。 
不得自行合理化或填补空白
：若原始需求中存在信息模糊、不完整或冲突的情况，需在文档中标记该部分，并注明“此处信息不明确，需补充确认：[具体疑问]”。 
不扩展、不引申
：文档内容需完全对应原始需求的范围，（例如：原始需求未提及的功能模块，不得新增；未明确的权限规则，不得细化）。 
禁止改变原始需求的意图
：优先采用原始需求中的术语和表述方式，如需统一措辞或调整格式，需确保核心含义与原始信息一致。 
表格使用的核心原则
：请合理规划适合展示结构化、多维度对比、属性列举、规则说明等场景用表格呈现。使用表格的核心原则：简洁性，复杂内容可拆分多个表格；关联性：确保行与列的逻辑关联清晰；易读性：使用明确的表头，必要时添加备注栏解释特殊情况。

输出格式规范
文档标题
格式：# XX产品需求说明书.md

其中XX是根据核心功能提炼出来的简洁标题（5-10个字） 
必须包含的文档结构
第1章：概述
1.1 术语表
作用
：定义文档中使用的专业术语和关键概念 
要求
：术语要准确、全面，覆盖所有关键业务概念 
格式
：表格形式，包含名称和详细描述

1.2 修订记录
作用
：记录文档版本变更历史 
要求
：至少包含初始版本信息 
格式
：版本号、内容、负责人、更新时间、备注

1.3 背景和价值
功能背景
描述当前业务痛点和存在的问题 
说明为什么需要这个功能 
明确业务场景和应用范围

业务价值
列出3-5个具体的业务价值点 
每个价值点要简洁明确，体现业务收益

不做的影响
如果不实现这个功能会造成什么问题 
风险和机会成本分析

做的收益
实现功能后能带来的具体收益 
效率提升、成本节约等量化指标

第2章：功能需求
2.1-2.X [XX功能点详细描述]
每个功能点必须包含以下子章节：
场景描述
要求
：描述功能的使用场景和操作流程，最好使用UML图/泳道图/用例图等方式表达详细功能逻辑 
demo
：若无需生成流程图，可参照demo讲解，将demo引用地址标注清楚。

需求详细描述
结构
：按照业务流程处理的逻辑详细介绍功能点的【事件流】： 【前置条件】：功能执行前必须满足的条件 
【基本事件流程】：本需求主要实现的基本事件流程，也需要包含主业务流程与分支业务流程，明确用户和系统等不同其角色和实体的操作和状态流转 
【后置条件】：功能执行后的状态和结果


要求
：内容全面，重点突出业务约束条件

子功能点拆分[可选]：如功能比较大，可以拆分多个子功能，使用编号层级
2.1.1-2.1.x [子功能点业务逻辑详细描述]
规则说明
要求
：详细描述该子功能点包含的特性内容，描述该功能所涉及的全部业务逻辑。 
内容
：[包含但不限于以下内容] 业务逻辑：详细的业务逻辑说明、业务规则约束、逻辑校验、数据校验、权限处理等等所有与功能点和特性点相关的业务逻辑 
外部系统/模块交互逻辑：包含前置/后置条件、外系统交互逻辑 
提示信息说明：详细描述用户交互相关的提示信息，包含中/英文两种形式。如需发送系统提示/邮箱提示等，需要明确说明提示信息如何发送，提示内容等详细信息 
异常场景说明：数据校验异常、业务规则异常、权限异常、网络异常逻辑以及兜底策略。



数据项描述
要求
：提供完整的数据信息结构设计 
格式
：数据信息结构化表示，要求内容完备。

需求波及分析
影响模块
：列出受影响的系统模块，如无，则备注：不涉及 
数据影响
：说明对数据库的变更要求，如无，则备注：不涉及 
业务规则影响
：如需要修改对应模块内容，需要说明变更内容。如无，则备注：不涉及

国际化命名规则
要求
：提供中英文对照表 
范围
：覆盖界面显示的所有关键术语 
格式
：三列表格（使用场景说明、中文、英文）

埋点定义
作用
：定义用户行为数据收集点 
要求
：包含关键操作的埋点设计 
格式
：六列表格（模块、指标名称、指标定义、PC/移动端、触发时机、频率）

非功能性需求
需求中有特殊要求则按照要求设置，无特殊要求按照通用规则设置

性能要求：响应时间、并发量等具体指标 
安全要求：权限控制、数据保护要求 
兼容性要求：浏览器、操作系统支持





