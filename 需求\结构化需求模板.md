全局信息
产品元信息：
产品名称（文档标题用）
关联需求/背景文档链接
背景与价值：
业务背景
收益与风险（简要说明）
用户分析：
新增角色（角色编码与说明、全局权限规则）、角色清单（可参照知识库存储历史角色清单，描述本需求波及权限规则） 
流程编排：
业务流程场景说明 
功能拆分：
功能拆分与模块关系说明 
按功能模块分析需求（每个功能需提供）
功能基本信息
功能名称
功能名称 
业务流程：
场景：
描述清楚目标用户/角色在入口路径（PC/移动）具体操作，系统需要处理的任务等 
前置条件/后置条件：
含异常前置（如权限、状态、外部依赖）以及后置处理 
数据描述：
数据项清单：包含但不限于 字段名（中/英）、字段Key、类型、必填、默认值、取值来源/接口、校验规则、显示/隐藏条件、可编辑性 
业务逻辑：
业务流程规则说明：
包括但不限于流程节点状态说明、业务校验规则、操作规则、判断逻辑 
评审信息说明：
包括但不限于评审人员获取、评审规则限制、评审 
回退机制：
包括但不限于退回流程状态处理、业务异常回退处理、兜底策略等 
系统提示信息与关键和交互说明
通知与消息：
邮件/站内/移动审批模板（触发点、接收人、标题、正文、跳转链接规则） 
外部系统对接说明：
如有外部系统对接，且无法在前置、后置条件中说明的内容，包括但不限于接口对接、消息传递等 如有特殊需说明集成接口：输入/输出、依赖服务、容错与超时策略 
需求波及分析：
对周边系统/流程/权限/报表的影响，如需同步修改波及点，需要明确说明（可拆分功能点说明） 
全局信息（基于知识库规则作补充）
非功能性需求：
性能、安全、兼容、易用、可维护、可扩展、可靠性、可测试性 
国际化词典：
全局提示语、按钮文案、错误信息（中英对照） 
埋点指标：
指标名称、触发点、端别（PC/移动）、频率、上报字段 

