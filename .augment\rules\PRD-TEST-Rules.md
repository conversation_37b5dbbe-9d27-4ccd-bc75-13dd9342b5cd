---
alwaysApply: false
type: "manual"
---
## 1. 需求模版要求规范

### 1.1 模版选择规范
**场景适配性原则**
- **问题**: 错误选择模版类型，导致内容结构不匹配需求场景
- **规则**: 
  - 新功能使用完整需求分析模版
  - 变更需求使用波及影响分析模版
  - 单功能点使用简化模版

### 1.2 整体规范
**分层内容一致性原则**
- **问题**: 分层不清晰，功能点拆分不明确，未按模版要求输出
- **规则**: 
  - 功能点必须独立拆分
  - 分层结构清晰
  - 严格按模版输出
  - 分层、内容要与DT模版一致

### 1.3 用例事件流规范
**事件流完整性原则**
- **问题**: 事件流描述不完整或逻辑不清晰
- **规则**: 
  - 基本事件流描述主要成功场景
  - 扩展事件流描述分支场景
  - 异常事件流描述错误处理
  - 每步独立可执行

### 1.4 前置条件明确性规范
**前置条件具体化原则**
- **问题**: 前置条件描述模糊
- **规则**: 
  - 明确用户角色
  - 明确数据状态
  - 明确环境配置
  - 前置条件必须可验证

### 1.5 约束条件完整性规范
**约束条件详细化原则**
- **问题**: 约束描述不具体
- **规则**: 
  - 明确业务约束
  - 明确数据约束
  - 明确权限约束
  - 明确性能约束
  - 所有约束均可验证

### 1.6 验收准则情境化规范
**场景化标准**
- **问题**: 验收准则不具体
- **规则**: 
  - 使用Given-When-Then格式
  - 覆盖正常场景
  - 覆盖异常场景
  - 覆盖边界场景
  - 标准可量化

### 1.7 变更影响评估规范
**影响范围完整性原则**
- **问题**: 波及影响分析不全面
- **规则**: 
  - 明确波及特性
  - 明确波及功能点
  - 明确变更点
  - 明确影响说明
  - 明确测试范围

### 1.8 视觉辅助完整性规范
**图形化描述标准化原则**
- **问题**: 原型图和流程图缺失或不详细
- **规则**: 
  - 关键功能需要低保真原型
  - 复杂流程需要流程图
  - 图文必须一致

## 2. 需求文档详情要求规范

### 2.1 权限描述规范
**角色权限明确化原则**
- **问题**: 用户角色描述模糊，权限边界不清晰
- **规则**: 
  - 明确每个角色及其权限
  - 描述权限继承/排斥关系
  - 明确数据权限范围

### 2.2 表格规范要求
**表格内容一致性原则**
- **问题**: 表格描述不一致，存在空值、使用省略词
- **规则**: 
  - 所有表格字段必须完整
  - 禁止"同上"等省略词
  - 统一命名规范

**表格字段完整性原则**
- **问题**: 字段填写不完整
- **规则**: 
  - 每个字段都要填写
  - 无默认值标注"无"
  - 组件类型统一

**表格结构规范原则**
- **问题**: 合并单元格导致AI识别困难
- **规则**: 
  - 禁止合并单元格
  - 按功能模块拆分表格
  - 每个表格有独立标题

### 2.3 链接处理规范
**链接内容处理原则**
- **问题**: 文档中嵌入链接，AI无法读取
- **规则**: 
  - 重要规则提炼为文档内容
  - 参考性链接可忽略

### 2.4 事件流描述规范
**事件流独立性原则**
- **问题**: 一句话包含太多信息，存在隐性描述
- **规则**: 
  - 每步独立描述
  - 初始状态明确
  - 避免隐含逻辑

### 2.5 字段约束规范
**输入限制明确化原则**
- **问题**: 输入框限制或规则不明确
- **规则**: 
  - 明确字符长度限制
  - 明确输入格式要求
  - 明确搜索规则
  - 所有输入框有要求描述

**通用规则定义原则**
- **问题**: 字段约束可复用
- **规则**: 
  - 定义通用字段约束规则
  - 建立字段类型标准库

### 2.6 组件描述规范
**组件类型一致性原则**
- **问题**: demo内容与实际不符
- **规则**: 
  - 组件类型描述与实际一致
  - 建立标准词汇表
  - 表格与文字一致

### 2.7 关联性建立规范
**文字与表格关联原则**
- **问题**: 表格和上下文缺乏关联
- **规则**: 
  - 明确UI元素显示位置和时机
  - 文字与表格数据对应

### 2.8 业务逻辑规范
**状态流转明确化原则**
- **问题**: 事件流与业务规则缺乏关联
- **规则**: 
  - 用流程图描述状态流转
  - 明确审批节点
  - 明确并行/串行逻辑
  - 明确回退撤回机制

### 2.9 完整性描述规范
**信息完整性原则**
- **问题**: 描述存在歧义和隐含信息
- **规则**: 
  - 明确所有状态变化
  - 避免模糊表述
  - 特殊角色行为单独说明

**结果状态明确化原则**
- **规则**: 
  - 明确每种状态下可执行操作
  - 审批规则需具体

### 2.10 语法规范
**禁止前端语法原则**
- **问题**: 有<>等导致无法读取
- **规则**: 不要有<>等前端语法字符

### 2.11 去重复原则
**信息独立性原则**
- **问题**: 存在重复描述
- **规则**: 
  - 避免信息重复
  - 内容独立存在

### 2.12 位置描述规范
**UI元素位置明确化原则**
- **问题**: 字段显示位置不明确
- **规则**: 
  - 明确每元素显示位置
  - 用相对位置描述

### 2.13 表格导入/导出规范
**详尽原则**
- **问题**: 导入/导出描述不全
- **规则**: 
  - 明确导入/导出规则
  - 明确校验内容
  - 明确最大数据量
  - 明确同步异步方式

### 2.14 文件上传规范
**明确边界原则**
- **问题**: 格式、大小、文件名长度等限制不明确
- **规则**: 
  - 明确文件格式
  - 明确文件大小限制
  - 明确文件名长度限制
  - 明确异常处理

## 3. 其他特殊要求规范

### 3.1 非功能性需求规范
**性能指标量化原则**
- **问题**: 性能要求描述模糊
- **规则**: 
  - 明确页面加载时间
  - 明确并发用户数
  - 明确大数据量处理标准
  - 提供具体数值

### 3.2 多语言支持规范
**国际化标准化原则**
- **问题**: 多语言需求描述不完整
- **规则**: 字段、提示语、按钮等国际化一一对应

### 3.3 安全防护规范
**安全机制明确化原则**
- **问题**: 安全要求描述模糊
- **规则**: 
  - 明确身份认证机制
  - 明确会话管理规则
  - 明确数据加密脱敏要求
  - 明确防护措施
  - 提供具体标准

### 3.4 环境兼容性规范
**兼容性标准明确化原则**
- **问题**: 兼容性要求描述不具体
- **规则**: 
  - 明确支持浏览器及具体版本
  - 明确支持分辨率
  - 明确支持操作系统及版本

### 3.5 系统集成规范
**接口交互明确化原则**
- **问题**: 外部系统接口依赖描述不清
- **规则**: 
  - 明确接口协议
  - 明确数据格式
  - 明确超时重试机制
  - 明确异常处理规则

---

**使用说明:**
1. 本规则用于PRD文档的评审和质量检查
2. 所有规则都是为了确保AI能够准确理解需求并生成正确的测试用例
3. 评审时需要逐项检查每个规则的执行情况
4. 不符合规则的PRD文档需要返回修改后再进行测试用例生成