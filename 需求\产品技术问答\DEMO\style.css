* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background-color: #f5f5f5;
    height: 100vh;
    overflow: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    height: 100vh;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 顶部标题 */
.header {
    padding: 20px 30px;
    border-bottom: 1px solid #f0f0f0;
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

/* 欢迎文本 */
.welcome-text {
    padding: 30px;
    background-color: #ffffff;
}

.welcome-text p {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    text-align: left;
}

/* 预设问题 */
.preset-questions {
    padding: 0 30px 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.preset-questions.hidden {
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
    max-height: 0;
    padding: 0 30px;
    overflow: hidden;
}

.question-btn {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 12px 18px;
    font-size: 14px;
    color: #495057;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.question-btn:hover {
    background-color: #e9ecef;
    border-color: #d6d9dc;
    transform: translateY(-1px);
}

.question-btn:active {
    transform: translateY(0);
}

/* 聊天区域 */
.chat-area {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    position: relative;
    z-index: 2;
}

.message {
    max-width: 85%;
    padding: 0;
    border-radius: 0;
    font-size: 14px;
    line-height: 1.6;
    word-wrap: break-word;
    margin-bottom: 20px;
}

.message.user {
    align-self: flex-end;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.message.user .message-content {
    background-color: #007AFF;
    color: white;
    padding: 12px 16px;
    border-radius: 18px;
    border-bottom-right-radius: 6px;
    max-width: 100%;
    word-wrap: break-word;
}

.message.assistant {
    align-self: flex-start;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
}

.message.assistant .message-content {
    background-color: transparent;
    color: #333;
    padding: 16px 0;
    border-radius: 0;
    max-width: 100%;
    width: 100%;
    border: none;
}

.message-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    opacity: 1;
}

.action-btn {
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 50%;
    padding: 6px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.action-btn:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
    color: #333;
}

.action-btn.active {
    background-color: #007AFF;
    border-color: #007AFF;
    color: white;
}

/* 新对话区域 */
.new-chat-section {
    padding: 15px 30px;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    width: 100%;
}

/* 底部区域 */
.bottom-section {
    border-top: 1px solid #f0f0f0;
    background-color: #ffffff;
}

.input-section {
    padding: 20px 30px;
}

.input-header {
    margin-bottom: 15px;
}

.common-qa {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.qa-icon {
    font-size: 16px;
}

.search-toggle-header {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 16px;
    padding: 4px 10px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.search-toggle-header:hover {
    border-color: #007AFF;
}

.search-toggle-header.active {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.input-container {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 25px;
    padding: 8px 15px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.input-container:focus-within {
    border-color: #007AFF;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

#questionInput {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    padding: 8px 12px;
    color: #333;
}

#questionInput::placeholder {
    color: #999;
}

.send-btn {
    background-color: #007AFF;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
}

.send-btn:hover {
    background-color: #0056CC;
    transform: scale(1.05);
}

.send-btn:active {
    transform: scale(0.95);
}

.send-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 新对话按钮样式 */
.clear-chat {
    font-size: 13px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    margin: 0 auto;
}

.clear-chat:hover {
    color: #007AFF;
}

.new-chat-icon {
    font-size: 16px;
    color: #007AFF;
}

/* 免责声明 */
.disclaimer {
    padding: 15px 30px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.disclaimer p {
    font-size: 12px;
    color: #999;
    text-align: center;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        height: 100vh;
        max-width: 100%;
    }
    
    .header, .welcome-text, .preset-questions, .chat-area, .input-section, .more-options, .disclaimer {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .message {
        max-width: 90%;
    }
    
    .question-btn {
        font-size: 13px;
        padding: 10px 15px;
    }
}

/* 滚动条样式 */
.chat-area::-webkit-scrollbar {
    width: 6px;
}

.chat-area::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    background-color: #f1f3f4;
    border-radius: 12px;
    border-bottom-left-radius: 4px;
    align-self: flex-start;
    max-width: 80%;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #999;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 参考文档引用样式 */
.reference {
    display: inline-block;
    background-color: #007AFF;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    text-align: center;
    font-size: 10px;
    line-height: 16px;
    margin-left: 2px;
    cursor: pointer;
    text-decoration: none;
    vertical-align: super;
    transition: all 0.2s ease;
}

.reference:hover {
    background-color: #0056CC;
    transform: scale(1.1);
}

.references-list {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.references-list h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.reference-item {
    margin: 8px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.reference-item a {
    color: #007AFF;
    text-decoration: none;
}

.reference-item a:hover {
    text-decoration: underline;
}

.reference-item.permission-required {
    opacity: 0.7;
}

.permission-doc-link {
    color: #999;
    font-style: italic;
    text-decoration: none;
    cursor: pointer;
}

.permission-doc-link:hover {
    color: #007AFF;
    text-decoration: underline;
}

/* 问题类型标签 */
.question-type {
    display: inline-block;
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-bottom: 8px;
}

.question-type.product-config { background-color: #e8f5e8; color: #2e7d32; }
.question-type.solution { background-color: #fff3e0; color: #f57c00; }
.question-type.command { background-color: #fce4ec; color: #c2185b; }
.question-type.metrics { background-color: #f3e5f5; color: #7b1fa2; }
.question-type.sales { background-color: #e0f2f1; color: #00796b; }
.question-type.knowledge { background-color: #e1f5fe; color: #0277bd; }
.question-type.lifecycle { background-color: #fff8e1; color: #f9a825; }
.question-type.case { background-color: #e8eaf6; color: #3f51b5; }
.question-type.market { background-color: #ffebee; color: #d32f2f; }

/* 搜索指示器 */
.search-indicator {
    display: inline-block;
    margin-left: 5px;
    font-size: 12px;
    color: #007AFF;
    font-weight: 500;
}

/* 联网搜索结果样式 */
.search-results {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f8ff;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

.search-results h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.search-icon {
    font-size: 16px;
}

.search-item {
    margin: 10px 0;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.search-item:last-child {
    border-bottom: none;
}

.search-title {
    font-weight: 500;
    color: #1976d2;
    margin-bottom: 4px;
}

.search-snippet {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 权限提示样式 */
.permission-notice {
    margin-top: 15px;
    padding: 12px 16px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    color: #856404;
    font-size: 13px;
    line-height: 1.5;
}

.permission-notice strong {
    color: #856404;
    font-weight: 600;
}

.permission-notice ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.permission-notice li {
    margin: 4px 0;
}

/* 权限申请弹窗样式 */
.permission-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.permission-modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.permission-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.permission-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.permission-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.permission-modal-close:hover {
    color: #666;
}

.permission-modal-body {
    padding: 24px;
}

.permission-modal-body p {
    margin: 12px 0;
    color: #333;
    line-height: 1.5;
}

.permission-form {
    margin-top: 20px;
}

.permission-form label {
    display: block;
    margin: 16px 0 6px 0;
    font-weight: 500;
    color: #333;
}

.permission-form textarea,
.permission-form input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
}

.permission-form textarea:focus,
.permission-form input:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.permission-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.btn-cancel,
.btn-submit {
    padding: 8px 20px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background-color: #f5f5f5;
}

.btn-submit {
    background-color: #007AFF;
    border-color: #007AFF;
    color: white;
}

.btn-submit:hover {
    background-color: #0056CC;
    border-color: #0056CC;
}
