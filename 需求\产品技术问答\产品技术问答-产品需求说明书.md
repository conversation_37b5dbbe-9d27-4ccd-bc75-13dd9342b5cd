# 产品技术问答-产品需求说明书.md

## 第1章：概述

### 1.1 术语表

| 名称 | 描述 |
|---|---|
| TSM库 | 语料源之一，用于对接并同步文档（来源：需求文档） |
| Info-Go | 语料源之一，支持知识库同步与权限验证（来源：需求文档） |
| iCenter | 产品线门户，作为语料源接入（来源：需求文档） |
| DN Studio | 知识库管理平台，用于构建、更新知识库并管理权限（来源：需求文档） |
| 知识切片（knowledgeChunks） | 将文档切分为文本块并生成向量索引的结构（来源：需求文档） |
| 权限级别 L1/L2/L3/L4 | 公开/内部/敏感/核心四级权限分级规则（来源：需求文档） |
| 10大专题 | 产品配置、方案介绍、产品指标、功能命令、销售策略、通识知识、路标生命周期、成功故事、市场信息、竞情信息（来源：需求文档） |
| 首字返回 | 回答首字在5秒内返回的性能指标（来源：需求文档） |
| 准确率评分（0-3分） | 用户对回答进行0-3分评分，统计2-3分占比（来源：需求文档） |
| 参考文档角标 | 在答案中以上标角标标注参考文档来源（来源：需求文档） |

### 1.2 修订记录

| 版本号 | 内容 | 负责人 | 更新时间 | 备注 |
|---|---|---|---|---|
| v0.1 | 基于《产品技术问答系统结构化需求文档》整理的PRD初稿 | 此处信息不明确，需补充确认：负责人 | 此处信息不明确，需补充确认：更新时间 | 首次建立 |

### 1.3 背景和价值

- 功能背景：基于TSM库、Info-Go、iCenter等多语料源构建智能问答；支持10大专题问答；通过DN Studio管理知识库与权限（来源：需求文档）。
- 业务价值：提升技术咨询准确率至90%+；5秒内首字返回；支持多语言问答（来源：需求文档）。
- 不做的影响：此处信息不明确，需补充确认：若不实现该系统对现有业务的具体影响与机会成本。
- 做的收益：效率提升、准确率可量化（示例：2-3分≥90%），响应更快（首字≤5秒）（来源：需求文档）。

## 第2章：功能需求

### 2.1 多语料源知识库管理（P1）

- 场景描述：系统自动对接TSM库、Info-Go、iCenter等语料源，通过DN Studio构建知识库，支持周期性自动更新与手动触发更新（来源：需求文档）。
- demo：此处信息不明确，需补充确认：DEMO引用地址/链接。

#### 2.1.1 需求详细描述（事件流）
- 前置条件：
  - TSM库、Info-Go、iCenter系统API可用；DN Studio已部署；语料访问权限已配置（来源）。
- 基本事件流程（主流程）：
  1) 定时器按更新周期触发扫描语料源变更；
  2) 检测增量变更并拉取变更文档；
  3) 生成/更新知识切片与向量索引；
  4) 同步更新权限映射；
  5) 更新完成后记录更新日志与状态（来源：自动更新机制）。
- 分支流程（手动）：
  - 管理员在控制台手动触发全量/增量更新；支持单个或批量文档；展示进度与结果（来源：手动更新机制）。
- 后置条件：索引与切片完成，更新日志记录（来源）。

#### 2.1.2 规则说明
- 自动更新机制：每周定时、增量更新、重建切片与向量、更新权限映射（来源）。
- 手动更新机制：支持全量/增量、单文档/批量、进度与结果反馈（来源）。
- 外部交互：
  - DN Studio：创建/更新/查询/触发更新接口（来源）。
  - TSM/Info-Go/iCenter：列表、内容获取、增量检测等（来源）。
- 提示信息：此处信息不明确，需补充确认：更新失败与异常提示文案（中/英）。
- 异常场景：此处信息不明确，需补充确认：数据校验异常、权限异常、网络异常及兜底策略。

#### 2.1.3 数据项描述
| 字段 | 类型 | 必填 | 说明 |
|---|---|---|---|
| corpusSource | Enum | 是 | TSM/INFO_GO/ICENTER |
| documentType | Enum | 是 | WORD/PDF/EXCEL/PPT/IMAGE |
| updateCycle | Integer | 否 | 默认7天，可配置 |
| lastUpdateTime | DateTime | 系统 | 系统维护 |
| corpusPermission | String | 是 | 对应权限级别 |
| documentPath | String | 是 | 源文档路径 |
| knowledgeChunks | Array | 系统 | 文本块与向量 |

#### 2.1.4 需求波及分析
- 影响模块：DN Studio、TSM、Info-Go、iCenter（来源）。
- 数据影响：需要构建/更新知识库索引与切片（来源）。
- 业务规则影响：权限映射维护（来源）。

---

### 2.2 多专题智能问答引擎（P1）

- 场景描述：用户自然语言提问，系统意图识别与专题分类；基于权限在授权语料源检索，5秒内返回带参考文档的回答（来源）。
- demo：此处信息不明确，需补充确认：DEMO引用地址/链接。

#### 2.2.1 需求详细描述（事件流）
- 前置条件：用户已验证；知识库可用；权限已加载（来源）。
- 基本事件流程（主流程）：
  1) 接收问题，检测用户语言；
  2) 意图识别并确定专题分类（10大专题之一）；
  3) 根据权限过滤可访问语料范围；
  4) 在授权语料中执行语义检索；
  5) 生成结构化回答，并以角标标注参考来源；
  6) 返回首字≤5秒，记录问答日志与响应时间；
  7) 支持用户评分，更新准确率统计（来源）。
- 后置条件：问答日志、准确率统计、行为分析（来源）。

#### 2.2.2 规则说明
- 问答流程规则：意图识别→权限过滤→语义检索→答案生成→参考标注（来源）。
- 准确率要求：两类专题2-3分占比均≥90%（来源）。
- 参考文档交互：使用上标角标，支持定位、预览与下载（基于权限）（来源）。
- 提示信息：参考“智能权限提示模板”（来源）。
- 异常场景：此处信息不明确，需补充确认：无匹配语料、检索超时、答案生成失败时的兜底策略。

#### 2.2.3 数据项描述
| 字段 | 类型 | 必填 | 说明 |
|---|---|---|---|
| questionText | String | 是 | 最大1000字符，支持中英文 |
| questionCategory | Enum | 系统 | 10大专题之一 |
| userLanguage | Enum | 自动 | ZH_CN/EN_US |
| responseTime | Integer | 系统 | 毫秒 |
| accuracyScore | Integer | 用户 | 0-3分 |
| referenceDocuments | Array | 系统 | 文档ID/标题/定位 |

#### 2.2.4 需求波及分析
- 影响模块：问答服务、检索服务、权限服务（来源：流程与要求）。
- 数据影响：新增问答日志、准确率统计、参考文档记录（来源）。
- 业务规则影响：权限过滤、分类规则（来源）。

---

### 2.3 分级权限控制与申请审批系统（P1）

- 场景描述：按用户权限返回内容；无权限时提示并引导申请，支持审批闭环（来源）。
- demo：此处信息不明确，需补充确认：DEMO引用地址/链接。

#### 2.3.1 需求详细描述（事件流）
- 前置条件：权限体系与审批流程已配置；用户角色已分配（来源）。
- 基本事件流程：
  1) 在问答前实时校验用户权限；
  2) 若无权限且检测到相关答案存在于受限语料，给出权限提示；
  3) 用户可一键申请，自动填充申请信息；
  4) 进入多级审批流程，状态实时同步；
  5) 审批通过后更新权限并通知用户（来源）。
- 后置条件：申请记录、审批状态、权限变更通知（来源）。

#### 2.3.2 规则说明
- 权限控制规则：实时验证、智能提示、申请引导、审批闭环（来源）。
- 提示信息：
  - 智能权限提示模板（中）：见需求文档模板内容（来源）。
  - 智能权限提示模板（英）：此处信息不明确，需补充确认：英文提示模板是否沿用或另行定义。
- 异常场景：此处信息不明确，需补充确认：审批超时/拒绝的处理与重试策略。

#### 2.3.3 数据项描述
| 字段 | 类型 | 必填 | 说明 |
|---|---|---|---|
| permissionLevel | Enum | 是 | L1/L2/L3/L4 |
| corpusAccess | Set | 否 | 拥有的语料源权限 |
| applicationReason | String | 申请时 | 最大500字符 |
| approvalWorkflow | Object | 系统 | 审批人/状态/时间 |
| permissionExpiry | DateTime | 否 | 临时权限过期时间 |

#### 2.3.4 需求波及分析
- 影响模块：权限服务、审批流、通知（来源）。
- 数据影响：新增权限申请与审批数据（来源）。
- 业务规则影响：权限与审批规则配置（来源）。

---

### 2.4 多模态内容支持与外网大模型集成（P2-P3）

- 场景描述：支持图片等多模态语料处理，集成外网大模型提供通识问答，避免在应用间切换（来源）。
- demo：此处信息不明确，需补充确认：DEMO引用地址/链接。

#### 2.4.1 需求详细描述（事件流）
- 前置条件：多模态处理能力部署；外网模型API可用；内外网安全策略配置（来源）。
- 基本事件流程：
  1) 接收多模态输入或通识查询；
  2) 判断是否需要外网模型；
  3) 如开启外网调用，转发请求并记录成本；
  4) 更新多模态内容索引与调用日志（来源）。
- 后置条件：索引更新、调用日志、成本统计（来源）。

#### 2.4.2 规则说明
- 外网调用开关：externalModelCall（默认false）（来源）。
- 成本统计：记录外网API调用成本（来源）。
- 异常场景：此处信息不明确，需补充确认：外网调用失败/超时的兜底策略。

#### 2.4.3 数据项描述
| 字段 | 类型 | 必填 | 说明 |
|---|---|---|---|
| multimodalContent | Object | 否 | 文本/图片/表格等 |
| externalModelCall | Boolean | 否 | 默认false |
| generalKnowledgeQuery | String | 否 | 通识问题 |
| costTracking | Object | 系统 | 外网调用成本 |

#### 2.4.4 需求波及分析
- 影响模块：多模态处理、外网模型代理、审计与计费（来源）。
- 数据影响：索引扩展、调用日志、成本数据（来源）。
- 业务规则影响：内外网访问策略（来源）。

## 第3章：国际化命名规则

三列表（使用场景说明、中文、英文）。以下为需求文档提供内容的映射：

| 使用场景说明 | 中文 | 英文 |
|---|---|---|
| 全局提示语 | 有问题尽管问我~ | Feel free to ask me anything~ |
| 按钮-发送 | 发送 | Send |
| 按钮-新对话 | 新对话 | New Chat |
| 按钮-联网搜索 | 联网搜索 | Web Search |
| 按钮-申请权限 | 申请权限 | Apply Permission |
| 按钮-复制 | 复制 | Copy |
| 按钮-转发 | 转发 | Share |
| 错误-权限不足 | 权限不足 | Insufficient permissions |
| 错误-网络错误 | 网络错误 | Network error |
| 错误-系统异常 | 系统异常 | System error |
| 错误-响应超时 | 响应超时 | Response timeout |

注：如需覆盖界面全部关键术语，请补充界面清单与上下文。

## 第4章：埋点定义

六列表（模块、指标名称、指标定义、PC/移动端、触发时机、频率）。依据需求文档的指标定义整理如下：

| 模块 | 指标名称 | 指标定义 | PC/移动端 | 触发时机 | 频率 |
|---|---|---|---|---|---|
| 此处信息不明确，需补充确认：模块 | 问题提交量 | 上报字段：用户ID、问题文本、问题分类、时间戳 | PC/移动 | 发送按钮点击 | 实时 |
| 此处信息不明确，需补充确认：模块 | 权限申请量 | 上报字段：用户ID、申请权限类型、申请理由、时间戳 | PC | 权限申请提交 | 实时 |
| 此处信息不明确，需补充确认：模块 | 搜索使用率 | 上报字段：用户ID、搜索状态、问题文本、时间戳 | PC/移动 | 搜索开关切换 | 实时 |
| 此处信息不明确，需补充确认：模块 | 用户满意度 | 上报字段：用户ID、评分、问题ID、回答ID、时间戳 | PC/移动 | 点赞/点踩 | 实时 |
| 此处信息不明确，需补充确认：模块 | 会话时长 | 上报字段：用户ID、会话ID、进入时间、离开时间、问答轮次 | PC/移动 | 页面进入/离开 | 会话级 |
| 此处信息不明确，需补充确认：模块 | 准确率统计 | 上报字段：问题分类、评分、参考文档、响应时间 | PC/移动 | 用户评分 | 实时 |
| 此处信息不明确，需补充确认：模块 | 系统性能指标 | 上报字段：响应时间、语料源、知识库版本、错误码 | 服务端 | 问答完成 | 实时 |

## 第5章：非功能性需求

- 性能要求（来源）：
  - 首字返回≤5秒；完整答案≤10秒；参考文档定位≤3秒。
  - 并发支持：500+同时在线；可用性：99.9%。
- 安全要求（来源）：
  - 四级权限体系，细粒度访问控制；敏感信息加密；完整审计日志。
- 兼容性要求：此处信息不明确，需补充确认：浏览器与操作系统支持范围。
- 扩展性要求（来源）：
  - 支持新增语料源与多文档格式；中英文问答与语言自动检测。

## 第6章：外部系统对接说明

- DN Studio接口（来源）：
  - 创建知识库：POST /api/knowledge-base/create
  - 更新语料：PUT /api/knowledge-base/update
  - 查询状态：GET /api/knowledge-base/status
  - 触发更新：POST /api/knowledge-base/refresh
- 语料源对接（来源）：
  - TSM：GET /tsm/api/documents、GET /tsm/api/document/{id}、GET /tsm/api/changes/since/{timestamp}
  - Info-Go：POST /info-go/api/sync、GET /info-go/api/permissions/{userId}

## 第7章：验收标准

- 语料源集成：TSM、Info-Go、iCenter对接完成。
- 文档格式支持：Word/PDF/Excel/PPT/图片。
- 自动更新：知识库7天周期自动更新。
- 问答准确率：10大专题2-3分占比≥90%。
- 响应性能：首字返回≤5秒达标率95%+。
- 权限控制：四级权限体系完整实现。
- 多语言支持：中英文问答准确识别与回答。

## 第8章：需求波及分析（总体）

- 系统集成影响：DN Studio平台、TSM库系统、Info-Go系统、iCenter门户、兴小助平台（来源）。
- 运维支持：建立准确率监控与优化闭环、7×24技术支持、定期业务测试（来源）。

## 附注

- 本PRD严格依据《产品技术问答系统结构化需求文档（优化版）》与《PRD-Rules》整理。对原始材料未明确之处，均以“此处信息不明确，需补充确认：…”标注，待业务侧补充后完善。
