export default {
  taskManagement: {
    // Labels
    label: {
      keywords: "Keywords",
      taskCode: "Task Code",
      taskName: "Task Name",
      country: "Country",
      customer: "Customer",
      project: "Project",
      branch: "Operator/Branch",
      dataSource: "Data Source",
    },
    // Messages
    msg: {
      taskNameExists: "Task name already exists, please change",
      plsEnterKeywords:
        "Please enter keywords (task name/customer name/project name)",
      plsEnterTaskCode: "Please enter task code",
      plsEnterTaskName: "Please enter task name",
      plsSelectCountry: "Please select country",
      plsSelectCustomer: "Please select customer",
      plsSelectProject: "Please select project",
      plsSelectBranch: "Please select operator/branch",
      plsSelectDataSource: "Please select data source",
      confirmDelete: "Are you sure you want to delete this task?",
      deleteSuccess: "Task deleted successfully",
      createSuccess: "Task created successfully",
      updateSuccess: "Task updated successfully",
      copySuccess: "Task copied successfully",
      formValidationError: "Please check if the form is filled correctly",
      uploadSuccess: "File uploaded successfully",
      uploadError: "File upload failed",
      fileTypeError: "Only Excel files are allowed",
      fileSizeError: "File size cannot exceed 10MB",
      quickResponseSuccess: "Quick response submitted successfully",
      quickResponseError: "Quick response submission failed",
      exportSuccess: "Export successfully",
      exportError: "Export failed",
      itemExport: "Item Export",
      itemImportTemplate: "Item Import Template",
      unit: 'items'
    },
    // Buttons
    button: {
      query: "Query",
      reset: "Reset",
      createTask: "Create Task",
      quickResponse: "Quick Response",
      response: "Response",
      edit: "Edit",
      copy: "Copy",
      delete: "Delete",
      save: "Save",
      cancel: "Cancel",
      confirm: "Confirm",
      submit: "Submit",
    },
    // Table columns
    column: {
      serialNumber: "No.",
      taskCode: "Task Code",
      taskName: "Task Name",
      country: "Country",
      customer: "Customer",
      project: "Project",
      responseProgress: "Response Progress",
      satisfaction: "Overall Satisfaction",
      createdBy: "Created By",
      createTime: "Create Time",
      updatedBy: "Updated By",
      updateTime: "Last Update Time",
      operation: "Operation",
    },
    // Dialogs
    dialog: {
      createTask: "Create Task",
      editTask: "Edit Task",
      copyTask: "Copy Task",
      quickResponse: "Quick Response",
    },
    // Forms
    form: {
      taskName: "Task Name",
      country: "Country",
      groupCustomer: "Group Customer/MTO",
      branch: "Operator/Branch",
      customer: "Customer",
      project: "Project",
      projectCode: "Project Code",
      projectName: "Project Name",
      customerInfo: "Customer Information",
      dataSource: "Data Source",
      responseFile: "Response Item File",
      copyResponseResults: "Copy Response Results",
      copyResponseResultsTip: "If checked, response results will be copied and item files cannot be imported",
      plsEnterTaskName: "Please enter task name",
      plsSelectCountry: "Please select country",
      plsSelectGroupCustomer: "Please select group customer/MTO",
      plsSelectBranch: "Please select operator/branch",
      plsSelectCustomer: "Please select customer",
      plsEnterProject: "Please enter project name",
      plsEnterProjectCode: "Please enter project code",
      plsEnterProjectName: "Please enter project name",
      plsEnterCustomerInfo: "Please enter customer information",
      plsSelectDataSource: "Please select data source",
      selectFile: "Select File",
      fileUploadTip:
        "Only Excel files are allowed, and file size should not exceed 10MB",
    },
    // Data source options
    dataSource: {
      docLib: "Document Library",
      projectDoc: "Project Documents",
      historySoc: "Historical SOC Documents",
    },
    // Quick Response
    quickResponse: {
      tips: "Items submitted through quick response will automatically create personal tasks and be placed in the personal task area. You can view and manage these tasks in task management. Personal tasks have the same functionality as normal tasks except they cannot be deleted.",
      product: "Product",
      country: "Country",
      branch: "Operator/Branch",
      customer: "Customer Name",
      project: "Project Name",
      itemInput: "Item Input",
      itemInputTip:
        "After entering item description, the system will automatically match relevant data in GBBS for response",
      plsSelectProduct: "Please select product",
      plsSelectCountry: "Please select country",
      plsSelectBranch: "Please select operator/branch",
      plsEnterCustomer: "Please enter customer name",
      plsEnterProject: "Please enter project name",
      plsEnterItems: "Please enter item description",
    },
    // Task Detail
  },
  taskDetail: {
    pageTitle: 'Task Detail',
    taskName: "Task Name",
    groupCustomer: "Group Customer/MTO",
    customerInfo: "Customer Info Supplement",
    country: "Country",
    branch: "Operator/Branch",
    customer: "Customer",
    project: "Project",
    dataSource: "Data Source",
    tabs: {
      itemManagement: "Item Management",
      dataAnalysis: "Data Analysis",
    },
    msg: {
      loadFailed: "Failed to load task details",
    },
    dataAnalysis: {
      comingSoon: "Data Analysis Feature Coming Soon",
      description:
        "This feature will provide detailed response data analysis and statistical reports",
    },
  },
  // Data Analysis
  dataAnalysis: {
    totalItems: "Total Items",
    respondedCount: "Responded",
    notRespondedCount: "Not Responded",
    respondingCount: "Responding",
    responseRate: "Response Rate",
    fcCount: "FC",
    pcCount: "PC",
    ncCount: "NC",
    satisfactionRate: "Satisfaction Rate",
    productProgress: "Product Progress",
    productName: "Product Name",
    refresh: "Refresh",
    msg: {
      loadFailed: "Failed to load data",
    },
  },
  // Item Management
  itemManagement: {
    search: {
      itemCode: "Code",
      itemDesc: "Item Description",
      product: "Product",
      status: "Response Status",
      tags: "Tags",
      assignee: "Assignee",
      plsEnterItemCode: "Please enter code",
      plsEnterItemDesc: "Please enter item description",
      plsSelectProduct: "Please select product",
      plsSelectStatus: "Please select response status",
      plsEnterTags: "Please enter tags",
      plsSelectAssignee: "Please select assignee",
      plsSelectResponse: "Please select response result",
      plsEnterResponseDesc: "Please enter response description",
      plsEnterReference: "Please enter reference",
      plsSelectResponseMethod: "Please select response method",
    },
    buttons: {
      startResponse: "Start Response",
      batchDelete: "Batch Delete",
      batchAddTags: "Batch Add Tags",
      batchRemoveTags: "Batch Remove Tags",
      addProduct: "Add Product",
      permissionManagement: "Permission Management",
      export: "Export",
      singleAdd: "Single Add",
      batchImport: "Batch Import",
      filter: "Filter",
      more: "More",
      query: "Query",
      reset: "Reset",
      save: "Save",
      cancel: "Cancel",
      confirm: "Confirm",
      submit: "Submit",
      viewReference: "View",
      editDetail: "Edit Detail",
      aiResponse: "AI Response",
      edit: "Edit",
      delete: "Delete",
      selectAll: "Select All",
      viewSelected: "View Selected",
      columnSetting: "Column Setting",
    },
    columns: {
      itemCode: "Code",
      itemDesc: "Item Description",
      tags: "Tags",
      status: "Response Status",
      product: "Product",
      response: "Response",
      assignee: "Assignee",
      responseMethod: "Response Method",
      responseDesc: "Response Description",
      source: "Source",
      reference: "Reference",
      remark: "Remark",
      lastUpdatedBy: "Last Updated By",
      lastUpdatedDate: "Last Updated Time",
      operation: "Operation",
    },
    responseMethodOptions:{
      MANUAL: 'Manual',
      AI: 'AI'
    },
    status: {
      notResponded: "Not Responded",
      responding: "Responding",
      responded: "Responded",
      answerFailed: "Answer Failed",
    },
    form: {
      itemCode: "Code",
      itemDesc: "Item Description",
      product: "Product",
      productPath: "Product Path",
      assignee: "Assignee",
      autoResponse: "Auto Response",
      remark: "Remark",
      overwriteOnDuplicate: "Overwrite on Duplicate",
      plsEnterItemCode: "Please enter code",
      plsEnterItemDesc: "Please enter item description",
      plsSelectProduct: "Please select product",
      plsSelectProductPath: "Please select product path",
      plsSelectAssignee: "Please select assignee",
      plsEnterRemark: "Please enter remark",
      enableAutoResponse: "Enable auto response",
      enableOverwrite: "Overwrite on duplicate",
    },
    dialog: {
      singleAdd: "Single Add",
      editItem: "Edit Item",
      batchImport: "Batch Import",
      batchAddTags: "Batch Add Tags",
      batchRemoveTags: "Batch Remove Tags",
    },
    addProduct: {
      title: "Add Product",
      product: "Product",
      plsSelectProduct: "Please select or enter product",
      tipNoDuplicate: "Do not select an existing product",
      confirmTip: "Adjust product to {product}. Do you want to continue?",
      confirmAll:
        "This will add product to ALL items. Are you sure you want to continue?",
      success: "Product added and responded successfully",
      failed: "Failed to add product and respond",
    },
    import: {
      tips: "Please select the Excel file to import, ensure the file format is correct and contains necessary fields",
      file: "Import File",
      selectFile: "Select File",
      fileTypeTip:
        "Only Excel files are allowed, and file size should not exceed 10MB",
      downloadTemplate: "Download Import Template",
    },
    tags: {
      newTags: "New Tags",
      removeTags: "Remove Tags",
      plsEnterTags: "Please enter or select tags",
      plsSelectRemoveTags: "Please select tags to remove",
    },
    msg: {
      loadFailed: "Failed to load item list",
      saveSuccess: "Save successful",
      saveFailed: "Save failed",
      deleteSuccess: "Delete successful",
      deleteFailed: "Delete failed",
      importSuccess: "Import successful",
      importFailed: "Import failed",
      uploadSuccess: "File upload successful",
      uploadError: "File upload failed",
      fileTypeError: "Only Excel files are allowed",
      confirmStartAllResponse:
        "No items selected, start response for all items?",
      startResponseSuccess: "Start response successful",
      startResponseFailed: "Start response failed",
      confirmBatchDelete:
        "This will delete ALL items. Are you sure you want to continue?",
      confirmDeleteSelected: "Are you sure you want to delete the selected items?",
      confirmDelete: "Are you sure you want to delete this item?",
      plsSelectTags: "Please select tags",
      addTagsSuccess: "Add tags successful",
      addTagsFailed: "Add tags failed",
      confirmBatchAddTagsAll:
        "This will add tags to ALL items. Are you sure you want to continue?",
      removeTagsSuccess: "Remove tags successful",
      removeTagsFailed: "Remove tags failed",
      confirmBatchRemoveTagsAll:
        "This will remove tags from ALL items. Are you sure you want to continue?",
      aiResponseSuccess: "AI response successful",
      aiResponseFailed: "AI response failed",
      unit: 'items'
    },
    permission: {
      title: "Permission Management",
      mode: "Mode",
      assign: "Assign Response",
      readonly: "Read-only Grant",
      operation: "Operation",
      add: "Add Read-only",
      remove: "Remove Read-only",
      users: "Users",
      plsSelectUsers: "Please select users",
      tipAssignAll: "No items selected: operation will apply to ALL items",
      tipReadonly:
        "Read-only grant does not require selecting items; you can add/remove directly",
      confirmAssignAll: "Confirm to assign ALL items to {users}?",
      assignSuccess: "Assign successful",
      assignFailed: "Assign failed",
      readonlySuccess: "Read-only updated successfully",
      readonlyFailed: "Read-only update failed",
    },
    // Product Tree Select
    productTreeSelect: {
      placeholder: "Please select product",
      noData: "No data",
      loadError: "Failed to load product data",
      searchError: "Failed to search products",
      searching: "Searching..."
    },
  },
  // Item Detail
  itemDetail: {
    backToTask: "Back to Task",
    itemDetail: "Item Detail",
    basicInfo: "Basic Information",
    operationHistory: "Operation History",
    save: "Save",
    aiResponse: "AI Response",
    edit: "Edit",
    cancelEdit: "Cancel Edit",
    back: "Back",
    form: {
      itemCode: "Code",
      itemDesc: "Item Description",
      product: "Product",
      assignee: "Assignee",
      tags: "Tags",
      status: "Status",
      responseResult: "Response Result",
      responseDesc: "Response Description",
      reference: "Reference",
      remark: "Remark",
      plsEnterItemCode: "Please enter code",
      plsEnterItemDesc: "Please enter item description",
      plsSelectProduct: "Please select product",
      plsSelectAssignee: "Please select assignee",
      plsEnterTags: "Please enter tags",
      plsSelectResponseResult: "Please select response result",
      plsEnterResponseDesc: "Please enter response description",
      plsEnterReference: "Please enter reference",
      plsEnterRemark: "Please enter remark",
    },
    msg: {
      loadFailed: "Failed to load item details",
      saveSuccess: "Save successful",
      saveFailed: "Save failed",
      aiResponseSuccess: "AI response successful",
      aiResponseFailed: "AI response failed",
    },
  },
};
