# 产品技术问答系统 DEMO

这是一个基于现代化设计风格的智能产品技术问答系统演示项目，具备完整的权限控制、多轮对话、联网搜索等企业级功能。

## 📖 项目概述

本系统是一个专为企业技术咨询设计的智能问答平台，能够为用户提供专业的产品技术信息查询服务。系统支持多种产品类型的问答，包括ZXCTN、M6000、CX66A、SNPG等系列产品，并具备完善的权限管理机制。

## ✨ 核心功能

### 🎯 基础功能
- **智能问答对话**：支持用户输入问题并获得AI智能回答
- **多轮对话支持**：智能理解上下文，支持连续对话和关联问答
- **9种问题类型支持**：产品配置、方案介绍、功能命令、指标查询、销售策略、通识知识、生命周期、成功案例、市场/竞情信息
- **预设问题**：提供常见技术问题的快速访问入口
- **实时交互**：模拟真实的聊天对话体验，包含打字指示器
- **响应式设计**：完美适配桌面端和移动端设备

### 🔐 权限控制系统
- **多层级权限管理**：支持basic、zxctn、m6000、cx66a、nokia_compare、price_sensitive、internal等多种权限类型
- **智能内容过滤**：根据用户权限自动屏蔽无权限访问的语料内容
- **权限申请流程**：用户可以直接申请所需权限，流程完整且用户友好
- **权限透明化**：明确告知用户哪些内容需要特殊权限访问

### 📚 参考文档系统
- **文档引用功能**：答案中包含角标引用，可点击跳转到对应参考文档
- **权限状态标识**：清楚区分有权限和需申请权限的文档资源
- **交互式文档访问**：支持点击权限受限文档直接进入申请流程

### 🌐 联网搜索功能
- **实时搜索切换**：支持开启/关闭联网搜索功能
- **智能搜索结果**：根据问题类型提供相关的产品对比、竞品分析、市场分析等搜索结果
- **搜索结果整合**：将搜索结果与AI回答有机结合展示

### 💬 交互功能
- **操作按钮**：每个回答都提供📋复制、📝复制为Markdown、📤转发、👍点赞、👎点踩功能
- **新对话功能**：支持清空当前对话历史，重新开始新的问答会话
- **上下文理解**：智能识别"刚才"、"那个"、"它的"等自然语言表达

## 🏗️ 技术架构

### 前端技术栈
- **HTML5**：语义化结构设计
- **CSS3**：现代样式和动画效果，响应式布局
- **JavaScript ES6+**：模块化编程，面向对象设计
- **无依赖**：纯前端实现，无需外部框架

### 核心模块
- **ChatSystem类**：主要的聊天系统控制器
- **权限管理模块**：权限检查、过滤和申请流程
- **多轮对话引擎**：上下文理解和关联回答生成
- **UI交互模块**：消息显示、动画效果、用户操作

## 📁 文件结构

```
产品技术问答系统DEMO/
├── index.html              # 主页面文件
├── style.css               # 样式文件
├── script.js               # JavaScript交互逻辑
└── README.md               # 项目文档
```

## 🚀 使用方法

### 快速启动
1. **直接打开**：双击 `index.html` 文件在浏览器中打开
2. **本地服务器**：使用任何HTTP服务器托管文件夹
3. **在线预览**：上传到Web服务器进行在线预览

### 功能使用指南

#### 基本问答
1. 进入系统后，点击预设问题或在输入框中输入问题
2. 系统会自动分类问题类型并提供专业回答
3. 每个回答都包含问题类型标签和相关参考文档

#### 多轮对话使用
```
用户：ZXCTN 6180H-A的尺寸是多少？
系统：高度1U(44.45mm)，宽度482.6mm，深度420mm，重量约8.5kg

用户：那它的功耗呢？
系统：ZXCTN 6180H-A功耗信息：
     典型功耗≤180W，最大功耗≤220W，待机功耗≤15W
     结合之前提到的1U尺寸，功耗密度适中

用户：价格怎么样？
系统：标准配置约15-20万元，包含基础软件许可
     批量采购可享受8-9折优惠，TCO较低
```

#### 权限控制体验
1. 点击CX66A相关问题，体验权限过滤功能
2. 在参考文档中点击【需申请权限】的条目
3. 填写申请理由并提交权限申请

#### 联网搜索使用
1. 点击"🌐 联网搜索"按钮激活搜索功能
2. 搜索按钮变为蓝色表示已激活
3. 提问时系统会额外提供搜索结果

## 🔧 自定义配置

### 修改用户权限
在 `script.js` 文件中找到权限设置：
```javascript
this.userPermissions = new Set(['basic', 'zxctn', 'public']); // 修改用户权限
```

### 添加新的产品数据
在 `setupMockResponses()` 方法中添加新的问答对：
```javascript
'新产品问题': {
    type: 'product-config',
    knowledgeSources: [
        {
            permission: 'required_permission',
            content: '回答内容...'
        }
    ],
    references: [...]
}
```

### 调整UI样式
在 `style.css` 文件中可以：
- 修改主题颜色
- 调整字体大小
- 更改布局尺寸
- 自定义动画效果

## 🎨 设计特色

### 现代化UI设计
- **简洁界面**：白色背景，清晰的视觉层次
- **问题类型标签**：彩色标签区分不同问题类型
- **消息气泡**：用户消息蓝色气泡，系统回答平铺显示
- **操作按钮**：圆形图标按钮，悬停时显示

### 颜色系统
- **主色调**：#007AFF（系统蓝）
- **问题类型颜色**：
  - 产品配置（绿色）：#2e7d32
  - 方案介绍（橙色）：#f57c00
  - 功能命令（粉色）：#c2185b
  - 指标查询（紫色）：#7b1fa2
  - 销售策略（青色）：#00796b
  - 通识知识（蓝色）：#0277bd
  - 生命周期（黄色）：#f9a825
  - 成功案例（靛蓝）：#3f51b5
  - 市场/竞情（红色）：#d32f2f

### 动画效果
- **页面加载**：元素渐入动画
- **按钮交互**：悬停缩放效果
- **消息发送**：打字指示器动画
- **权限弹窗**：模态框淡入效果

## 📊 数据结构

### 权限系统架构
```
权限层级：
├── basic（基础权限）
│   ├── 通用信息
│   ├── 公开规格
│   └── 基础介绍
├── 产品系列权限
│   ├── zxctn（ZXCTN系列）
│   ├── m6000（M6000系列）
│   ├── cx66a（CX66A系列）
│   └── snpg（SNPG系列）
├── 敏感信息权限
│   ├── nokia_compare（竞品对比）
│   ├── price_sensitive（价格信息）
│   └── internal（内部文档）
└── public（公开信息）
```

### 多轮对话数据
```
对话历史结构：
{
    role: 'user'|'assistant',
    content: '对话内容',
    type: '问题类型',
    timestamp: Date
}
```

## 🌟 产品亮点

### 1. 企业级权限控制
- 真实的多层级权限管理
- 完整的权限申请工作流
- 透明的权限状态展示

### 2. 智能多轮对话
- 上下文关键词识别
- 产品关联对话支持
- 自然语言理解能力

### 3. 专业技术回答
- 真实产品参数数据
- 详细技术规格信息
- 准确的价格和配置数据

### 4. 完整用户体验
- 响应式界面设计
- 丰富的交互功能
- 专业的视觉效果

## 🔍 测试指南

### 基础功能测试
1. **问答测试**：点击各个预设问题，验证回答准确性
2. **类型标签**：确认问题类型标签正确显示
3. **参考文档**：点击角标验证文档跳转功能

### 多轮对话测试
1. **上下文测试**：
   ```
   步骤1：询问"ZXCTN 6180H-A的尺寸"
   步骤2：追问"那功耗呢？"
   步骤3：继续问"价格怎么样？"
   验证：每次回答都关联前面的产品信息
   ```

2. **关键词识别**：
   ```
   测试词汇："刚才"、"那个"、"它的"、"详细"、"更多"
   验证：系统能正确理解并提供上下文回答
   ```

### 权限控制测试
1. **权限过滤**：询问CX66A优势，验证权限提示显示
2. **权限申请**：点击权限受限文档，测试申请流程
3. **权限状态**：验证有权限和无权限文档的不同显示

### 联网搜索测试
1. **搜索激活**：点击联网搜索按钮，验证状态变化
2. **搜索结果**：询问对比类问题，验证搜索结果展示
3. **搜索集成**：确认搜索结果与AI回答的整合效果

## 🛠️ 扩展开发

### 集成真实AI服务
1. 替换 `generateResponse` 方法中的模拟数据
2. 接入真实的AI API服务
3. 实现真实的权限验证系统

### 添加新功能
1. **语音输入**：集成语音识别功能
2. **文件上传**：支持文档和图片上传
3. **用户认证**：添加登录和用户管理
4. **数据存储**：集成数据库存储对话历史

### 性能优化
1. **懒加载**：大量数据的分页加载
2. **缓存机制**：常用回答的本地缓存
3. **压缩优化**：代码和资源文件压缩

## 🌐 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 基础问答功能
- ✅ 多轮对话支持
- ✅ 权限控制系统
- ✅ 联网搜索功能
- ✅ 参考文档系统
- ✅ 操作按钮功能
- ✅ 响应式设计

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目仅用于演示目的。实际使用时请根据具体需求调整代码和数据。

## 🔗 相关链接

- 项目演示：直接打开 `index.html`
- 技术支持：请通过系统内的权限申请功能联系
- 功能反馈：欢迎提出改进建议

## 📞 技术支持

如需技术支持或有任何问题，请：
1. 查看本README文档
2. 检查浏览器控制台错误信息
3. 通过系统内置的权限申请功能联系技术团队

---

**注意事项：**
- 本系统为演示版本，包含的产品数据和价格信息仅供参考
- 权限申请功能为模拟实现，实际部署时需要集成真实的权限管理系统
- 建议在生产环境中添加适当的安全措施和数据验证

**开发团队：** AI助手开发
**最后更新：** 2024年