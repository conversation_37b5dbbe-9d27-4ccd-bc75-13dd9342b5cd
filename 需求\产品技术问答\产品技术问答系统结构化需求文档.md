# 产品技术问答系统需求文档（优化版）

## 全局信息

### 产品元信息
**产品名称：** 营销智能伙伴 - 产品技术问答系统  
**关联需求/背景文档链接：** 
- 产品技术问答DEMO实现
- [产品技术问答DOD明细](https://i.zte.com.cn/index/ispace/#/space/c1dc275a76454318b40ef523f0e26ec4/wiki/page/808219fd70874f7a9781eca6da83826d/view)
- [BN智能问答语料源盘点](https://i.zte.com.cn/index/ispace/#/space/c1dc275a76454318b40ef523f0e26ec4/wiki/page/32a5c6846e634da4a879619cf12fdef6/view)

**项目时间节点：** 2025年9月30日完成核心功能

### 背景与价值
**业务背景：**
- 基于TSM库、Info-Go、iCenter产品线门户等多语料源构建智能问答系统
- 支持产品配置、方案介绍、产品指标、功能命令、销售策略等10大专题问答
- 通过DN Studio知识库管理，实现语料源自动更新和权限分级控制

**收益与风险：**
- 收益：提升技术咨询准确率至90%+，实现5秒内首字返回，支持多语言问答
- 风险：多语料源集成复杂性，权限控制精细化要求，准确率达标挑战

### 用户分析
**角色权限体系：**
- **销售人员（SALES）**：产品介绍、销售策略、竞情信息权限
- **技术工程师（TECH）**：产品配置、功能命令、产品指标权限  
- **产品经理（PM）**：全量产品信息、路标生命周期权限
- **客户（CUSTOMER）**：公开产品信息、通识知识权限

**权限分级规则：**
- L1-公开权限：通识知识、基础产品介绍
- L2-内部权限：详细技术规格、配置参数
- L3-敏感权限：价格策略、竞情分析、内部路标
- L4-核心权限：成本分析、战略规划

### 流程编排
**核心业务流程：**
1. **语料源集成** → TSM库/Info-Go/iCenter对接 → DN Studio知识库构建
2. **用户提问** → 意图识别 → 权限验证 → 语料源检索
3. **答案生成** → 内容过滤 → 参考文档标注 → 5秒内返回
4. **权限控制** → 无权限提示 → 权限申请流程 → 审批闭环

## 按功能模块分析需求

### 功能1：多语料源知识库管理

#### 功能基本信息
**功能名称：** 多语料源集成与知识库管理系统
**优先级：** P1（核心功能）

#### 业务流程
**场景：**
系统自动对接TSM库、Info-Go、iCenter等语料源，通过DN Studio构建知识库，支持周期性自动更新和手动触发更新。

**前置条件：**
- TSM库、Info-Go、iCenter系统API可用
- DN Studio平台部署完成
- 语料源访问权限已配置

**后置条件：**
- 知识库索引更新完成
- 语料切片转换完成
- 更新日志记录

#### 数据描述
**数据项清单：**
- **corpusSource（语料源）**：Enum，必填，TSM/INFO_GO/ICENTER
- **documentType（文档类型）**：Enum，必填，WORD/PDF/EXCEL/PPT/IMAGE
- **updateCycle（更新周期）**：Integer，默认7天，可配置
- **lastUpdateTime（最后更新时间）**：DateTime，系统维护
- **corpusPermission（语料权限）**：String，必填，对应权限级别
- **documentPath（文档路径）**：String，必填，源文档位置
- **knowledgeChunks（知识切片）**：Array，系统生成，包含文本块和向量

#### 业务逻辑
**自动更新机制：**
1. 每周定时扫描语料源变更
2. 增量更新变更文档
3. 重新生成知识切片和向量索引
4. 更新权限映射关系

**手动更新机制：**
- 知识库管理员可手动触发全量/增量更新
- 支持单个文档或批量文档更新
- 提供更新进度和结果反馈

### 功能2：智能问答引擎

#### 功能基本信息
**功能名称：** 多专题智能问答引擎
**优先级：** P1（核心功能）

#### 业务流程
**场景：**
用户通过自然语言提问，系统进行意图识别和专题分类，基于用户权限从对应语料源检索答案，5秒内返回带参考文档的回答。

**前置条件：**
- 用户身份验证完成
- 知识库数据可用
- 用户权限已加载

**后置条件：**
- 记录问答日志
- 更新准确率统计
- 触发用户行为分析

#### 数据描述
**数据项清单：**
- **questionText（问题文本）**：String，必填，最大1000字符，支持中英文
- **questionCategory（问题分类）**：Enum，系统识别，包含10大专题
- **userLanguage（用户语言）**：Enum，自动检测，ZH_CN/EN_US
- **responseTime（响应时间）**：Integer，系统统计，单位毫秒
- **accuracyScore（准确率评分）**：Integer，用户评价，0-3分
- **referenceDocuments（参考文档）**：Array，包含文档ID、标题、定位信息

**10大专题分类：**
1. 产品配置（product-config）
2. 产品和方案介绍（solution）  
3. 产品指标（metrics）
4. 功能命令（command）
5. 销售策略（sales）
6. 通识知识（knowledge）
7. 路标和生命周期（lifecycle）
8. 成功故事（case）
9. 市场信息（market）
10. 竞情信息（competition）

#### 业务逻辑
**问答流程规则：**
1. **意图识别**：基于关键词和语义分析识别问题专题
2. **权限过滤**：根据用户权限确定可访问的语料源范围
3. **语义检索**：在授权语料源中进行向量相似度检索
4. **答案生成**：结合检索结果生成结构化回答
5. **参考标注**：在答案中以角标形式标注参考文档来源

**准确率要求：**
- 第一类专题（产品配置、产品介绍、产品指标、功能命令、销售策略、通识知识）：2-3分占比≥90%
- 第二类专题（路标生命周期、成功故事、市场信息、竞情信息）：2-3分占比≥90%

### 功能3：权限控制与申请系统

#### 功能基本信息
**功能名称：** 分级权限控制与申请审批系统
**优先级：** P1（核心功能）

#### 业务流程
**场景：**
系统根据用户权限返回对应内容，当用户权限不足时，智能提示缺失权限并引导申请流程，支持审批闭环管理。

**前置条件：**
- 权限体系已建立
- 审批流程已配置
- 用户角色已分配

**后置条件：**
- 权限申请记录
- 审批状态更新
- 权限变更通知

#### 数据描述
**数据项清单：**
- **permissionLevel（权限级别）**：Enum，必填，L1/L2/L3/L4
- **corpusAccess（语料访问权限）**：Set，用户拥有的语料源权限
- **applicationReason（申请理由）**：String，申请时必填，最大500字符
- **approvalWorkflow（审批流程）**：Object，包含审批人、审批状态、审批时间
- **permissionExpiry（权限过期时间）**：DateTime，可选，支持临时权限

#### 业务逻辑
**权限控制规则：**
1. **实时权限验证**：每次问答前验证用户当前权限
2. **智能权限提示**：当检测到无权限语料中有相关答案时，提示申请对应权限
3. **权限申请引导**：提供一键申请功能，自动填充申请信息
4. **审批闭环管理**：支持多级审批，状态实时同步

### 功能4：多模态支持与外网集成

#### 功能基本信息
**功能名称：** 多模态内容支持与外网大模型集成
**优先级：** P2-P3（增强功能）

#### 业务流程
**场景：**
支持图片等多模态语料处理，集成外网大模型提供通识问答能力，避免用户在不同应用间切换。

**前置条件：**
- 多模态处理能力部署
- 外网大模型API可用
- 内外网安全策略配置

**后置条件：**
- 多模态内容索引更新
- 外网调用日志记录
- 成本统计更新

#### 数据描述
**数据项清单：**
- **multimodalContent（多模态内容）**：Object，包含文本、图片、表格等
- **externalModelCall（外网模型调用）**：Boolean，用户选择，默认false
- **generalKnowledgeQuery（通识查询）**：String，外网模型处理的问题
- **costTracking（成本跟踪）**：Object，记录外网API调用成本

## 系统提示信息与关键交互说明

### 性能要求
**响应时间要求：**
- 首字返回时间：≤5秒
- 完整答案返回：≤10秒
- 参考文档定位：≤3秒

### 权限提示优化
**智能权限提示模板：**
```
基于您的问题，我找到了部分相关信息：
[显示有权限的内容]

💡 **权限提示**：检测到{语料源名称}中有更完整的相关信息，但您当前没有访问权限。
要获得更全面的回答，请申请【{具体权限名称}】权限。

[一键申请权限按钮]
```

### 参考文档交互
**参考文档标注规则：**
- 答案中使用上标角标：`<sup class="reference" data-ref="1">1</sup>`
- 点击角标可快速定位到参考文档列表
- 点击参考文档可跳转到源文档具体位置
- 支持文档预览和下载（基于权限）

## 外部系统对接说明

### DN Studio集成
**知识库管理接口：**
- 创建知识库：POST /api/knowledge-base/create
- 更新语料：PUT /api/knowledge-base/update
- 查询状态：GET /api/knowledge-base/status
- 触发更新：POST /api/knowledge-base/refresh

### 语料源对接
**TSM库对接：**
- 文档列表获取：GET /tsm/api/documents
- 文档内容获取：GET /tsm/api/document/{id}
- 增量更新检测：GET /tsm/api/changes/since/{timestamp}

**Info-Go对接：**
- 知识库同步：POST /info-go/api/sync
- 权限验证：GET /info-go/api/permissions/{userId}

## 需求波及分析

### 系统集成影响
1. **DN Studio平台**：需要支持多语料源对接和知识库管理
2. **TSM库系统**：需要提供API接口和权限映射
3. **Info-Go系统**：需要支持知识库同步和权限验证
4. **iCenter门户**：需要提供语料访问接口
5. **兴小助平台**：需要支持意图识别和应用路由

### 运维支持要求
**专人对接机制：**
- 配置专门的运维团队负责系统维护
- 建立准确率监控和优化闭环机制
- 提供7×24小时技术支持
- 定期进行业务测试和准确率评估

## 非功能性需求

### 性能要求
- **响应时间**：首字返回≤5秒，完整回答≤10秒
- **并发支持**：支持500+用户同时在线
- **可用性**：99.9%系统可用性
- **准确率**：核心专题2-3分占比≥90%

### 安全要求
- **权限控制**：四级权限体系，细粒度访问控制
- **数据安全**：敏感信息加密存储和传输
- **审计日志**：完整的用户行为和权限使用日志

### 扩展性要求
- **语料源扩展**：支持新增语料源类型
- **文档格式扩展**：支持Word/PDF/Excel/PPT/图片等多格式
- **多语言支持**：中英文问答，支持语言自动检测

## 验收标准

### 核心验收指标
1. **语料源集成**：TSM库、Info-Go、iCenter三大语料源完成对接
2. **文档格式支持**：Word、PDF、Excel、PPT、图片格式全支持
3. **自动更新**：知识库7天周期自动更新机制
4. **问答准确率**：10大专题2-3分占比均达90%+
5. **响应性能**：首字返回时间≤5秒达标率95%+
6. **权限控制**：四级权限体系完整实现
7. **多语言支持**：中英文问答准确识别和回答

### 业务验收流程
1. **功能验收**：基于DOD明细逐项验收
2. **性能验收**：压力测试和响应时间验证
3. **准确率验收**：业务用户测试，参考历史测试方案
4. **集成验收**：与各语料源系统集成测试
5. **用户验收**：实际业务场景用户体验测试

## 国际化词典

### 全局提示语
- **中文**：有问题尽管问我~
- **英文**：Feel free to ask me anything~

### 按钮文案
- **发送**：Send / 发送
- **新对话**：New Chat / 新对话
- **联网搜索**：Web Search / 联网搜索
- **申请权限**：Apply Permission / 申请权限
- **复制**：Copy / 复制
- **转发**：Share / 转发

### 错误信息
- **权限不足**：Insufficient permissions / 权限不足
- **网络错误**：Network error / 网络错误
- **系统异常**：System error / 系统异常
- **响应超时**：Response timeout / 响应超时

## 埋点指标

### 关键指标定义
1. **问题提交量**
   - 触发点：发送按钮点击
   - 端别：PC/移动
   - 频率：实时
   - 上报字段：用户ID、问题文本、问题分类、时间戳

2. **权限申请量**
   - 触发点：权限申请提交
   - 端别：PC
   - 频率：实时
   - 上报字段：用户ID、申请权限类型、申请理由、时间戳

3. **搜索使用率**
   - 触发点：搜索开关切换
   - 端别：PC/移动
   - 频率：实时
   - 上报字段：用户ID、搜索状态、问题文本、时间戳

4. **用户满意度**
   - 触发点：点赞/点踩
   - 端别：PC/移动
   - 频率：实时
   - 上报字段：用户ID、评分、问题ID、回答ID、时间戳

5. **会话时长**
   - 触发点：页面进入/离开
   - 端别：PC/移动
   - 频率：会话级
   - 上报字段：用户ID、会话ID、进入时间、离开时间、问答轮次

6. **准确率统计**
   - 触发点：用户评分
   - 端别：PC/移动
   - 频率：实时
   - 上报字段：问题分类、评分、参考文档、响应时间

7. **系统性能指标**
   - 触发点：问答完成
   - 端别：服务端
   - 频率：实时
   - 上报字段：响应时间、语料源、知识库版本、错误码

## 附录

### DOD验收对照表
基于[产品技术问答DOD明细](https://i.zte.com.cn/index/ispace/#/space/c1dc275a76454318b40ef523f0e26ec4/wiki/page/808219fd70874f7a9781eca6da83826d/view)的核心要求：

| DOD项目 | 需求文档对应章节 | 验收标准 |
|---------|------------------|----------|
| 语料源集成 | 功能1：多语料源知识库管理 | TSM库、Info-Go、iCenter对接完成 |
| 文档格式支持 | 数据描述-documentType | Word/PDF/Excel/PPT/图片全支持 |
| 自动更新机制 | 业务逻辑-自动更新机制 | 7天周期自动更新 |
| 问答准确率 | 业务逻辑-准确率要求 | 10大专题2-3分占比≥90% |
| 响应时间 | 性能要求 | 首字返回≤5秒 |
| 权限控制 | 功能3：权限控制与申请系统 | 四级权限体系实现 |
| 多语言支持 | 数据描述-userLanguage | 中英文自动识别和回答 |

### 历史测试数据参考
根据20240830版本测试结果，当前各专题准确率基线：
- 产品和方案介绍：78.79%（2-3分占比）
- 产品指标：88.57%
- 功能命令：78.38%
- 销售策略：85.71%
- 通识知识：91.43%
- 产品配置：68.57%（需重点优化）

目标：所有专题达到90%+的2-3分占比。
