// 产品技术问答系统 JavaScript - 优化版

class ChatSystem {
    constructor() {
        this.chatArea = document.getElementById('chatArea');
        this.questionInput = document.getElementById('questionInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.searchToggle = document.getElementById('searchToggle');
        this.presetQuestions = document.querySelectorAll('.question-btn');
        this.presetQuestionsContainer = document.querySelector('.preset-questions');
        this.clearChatBtn = document.getElementById('clearChat');
        this.newChatSection = document.getElementById('newChatSection');
        
        this.isSearchEnabled = false;
        this.hasConversation = false;
        this.conversationHistory = [];
        
        // 权限控制系统 - 演示不同权限情况
        // 情况1：有部分权限 - new Set(['basic', 'zxctn', 'public', 'cx66a'])
        // 情况2：无CX66A权限 - new Set(['basic', 'zxctn', 'public']) 
        this.userPermissions = new Set(['basic', 'zxctn', 'public', 'cx66a']); // 当前：有CX66A权限，无Nokia对比和价格权限
        this.setupPermissionSystem();
        
        this.initEventListeners();
        this.initPresetQuestions();
        this.setupMockResponses();
        this.setupQuestionTypes();
    }

    initEventListeners() {
        // 发送按钮点击事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 搜索切换按钮
        this.searchToggle.addEventListener('click', () => this.toggleSearch());
        
        // 输入框回车事件
        this.questionInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 输入框变化事件
        this.questionInput.addEventListener('input', (e) => {
            this.updateSendButton(e.target.value.trim());
        });

        // 清空对话按钮事件
        this.clearChatBtn.addEventListener('click', () => {
            this.clearChat();
        });
    }

    toggleSearch() {
        this.isSearchEnabled = !this.isSearchEnabled;
        this.searchToggle.classList.toggle('active', this.isSearchEnabled);
        
        // 不再添加额外的搜索指示器，只改变按钮状态
    }

    initPresetQuestions() {
        this.presetQuestions.forEach(btn => {
            btn.addEventListener('click', () => {
                const question = btn.getAttribute('data-question');
                this.sendPresetQuestion(question);
            });
        });
    }

    setupPermissionSystem() {
        // 语料权限定义
        this.knowledgePermissions = {
            'basic': ['通用信息', '公开规格', '基础介绍'],
            'zxctn': ['ZXCTN系列产品', 'ZXCTN技术规格', 'ZXCTN价格信息'],
            'm6000': ['M6000系列产品', 'M6000技术规格', 'M6000价格信息'],
            'cx66a': ['CX66A产品信息', 'CX66A技术优势', 'CX66A竞争分析'],
            'snpg': ['SNPG产品信息', 'SNPG功能特性'],
            'nokia_compare': ['Nokia对比分析', '竞品技术对比'],
            'price_sensitive': ['详细价格信息', '成本分析', 'ROI数据'],
            'internal': ['内部技术文档', '研发信息', '战略规划'],
            'public': ['公开市场信息', '行业标准', '通用知识']
        };
    }

    setupQuestionTypes() {
        // 问题类型定义
        this.questionTypes = {
            'product-config': '产品配置',
            'solution': '方案介绍', 
            'command': '功能命令',
            'metrics': '指标查询',
            'sales': '销售策略',
            'knowledge': '通识知识',
            'lifecycle': '生命周期',
            'case': '成功案例',
            'market': '市场/竞情信息'
        };

        // 问题分类规则
        this.questionClassifiers = {
            'product-config': ['配置', '参数', '设置', '规格', '型号', '版本'],
            'solution': ['方案', '解决方案', '架构', '设计', '集成'],
            'command': ['命令', '操作', '功能', '使用方法', '怎么'],
            'metrics': ['指标', '性能', '速度', '容量', '尺寸', '温度', '功耗'],
            'sales': ['价格', '成本', '优势', '对比', '竞争', '销售'],
            'knowledge': ['原理', '技术', '标准', '协议', '概念'],
            'lifecycle': ['维护', '升级', '支持', '生命周期', 'EOL'],
            'case': ['案例', '应用', '项目', '客户', '成功'],
            'market': ['市场', '行业', '趋势', '竞品', '分析']
        };
    }

    classifyQuestion(question) {
        for (const [type, keywords] of Object.entries(this.questionClassifiers)) {
            if (keywords.some(keyword => question.includes(keyword))) {
                return type;
            }
        }
        return 'knowledge'; // 默认类型
    }

    setupMockResponses() {
        // 模拟回答数据库 - 包含权限控制
        this.mockResponses = {
            '我司CX66A(E)相比Nokia 1830 PSS-16i有哪些优势？': {
                type: 'sales',
                knowledgeSources: [
                    {
                        permission: 'cx66a',
                        content: `**CX66A(E)技术优势：**
• 更高的端口密度和处理能力<sup class="reference" data-ref="1">1</sup>
• 支持更大的转发容量<sup class="reference" data-ref="2">2</sup>
• 更低的时延性能<sup class="reference" data-ref="1">1</sup>

**技术特性：**
• 支持最新的SDN/NFV技术<sup class="reference" data-ref="3">3</sup>
• 更完善的QoS保障机制
• 更强的网络虚拟化能力`
                    },
                    {
                        permission: 'nokia_compare',
                        content: `**与Nokia 1830对比分析：**
• 端口密度：CX66A支持288×10GE，Nokia仅支持192×10GE
• 功耗效率：CX66A每Gbps功耗0.8W，Nokia为1.2W
• 时延控制：CX66A端到端时延<1ms，优于Nokia方案`
                    },
                    {
                        permission: 'price_sensitive',
                        content: `**成本优势分析：**
• 更具竞争力的价格<sup class="reference" data-ref="4">4</sup>
• 更低的总体拥有成本(TCO)
• 设备采购成本比Nokia低25-30%
• 5年TCO比Nokia方案节省约40%`
                    }
                ],
                references: [
                    { id: 1, title: "CX66A(E)产品技术规格书", url: "#" },
                    { id: 2, title: "转发性能对比测试报告", url: "#" },
                    { id: 3, title: "SDN/NFV技术白皮书", url: "#" },
                    { id: 4, title: "产品价格对比分析", url: "#" }
                ]
            },

            'What is the operation temperature of ZXCTN 6120H-S?': {
                type: 'metrics',
                knowledgeSources: [
                    {
                        permission: 'zxctn',
                        content: `**ZXCTN 6120H-S 工作温度规格：**

**Operating Temperature:**
• Normal operating: -5°C to +50°C (23°F to 122°F)<sup class="reference" data-ref="1">1</sup>
• Extended operating: -10°C to +55°C (14°F to 131°F)<sup class="reference" data-ref="1">1</sup>

**Storage Temperature:**
• -40°C to +70°C (-40°F to 158°F)

**Relative Humidity:**
• 5% to 95% (non-condensing)<sup class="reference" data-ref="2">2</sup>`
                    },
                    {
                        permission: 'internal',
                        content: `**内部测试数据：**
• 极限工作温度：-15°C to +60°C（特殊环境下）
• 平均无故障工作时间：>100,000小时
• 环境适应性等级：IP40防护等级

**认证信息：**
• 符合 IEC 60068 标准<sup class="reference" data-ref="3">3</sup>
• 通过工业级环境测试认证`
                    }
                ],
                references: [
                    { id: 1, title: "ZXCTN 6120H-S环境规格文档", url: "#" },
                    { id: 2, title: "湿度测试报告", url: "#" },
                    { id: 3, title: "IEC 60068标准认证", url: "#" }
                ]
            },

            'SNPG(E)是否支持电监控？': {
                type: 'product-config',
                content: `**SNPG(E) 电监控功能支持详情：**

**✅ 完全支持电监控功能**

**监控功能：**
• 实时电压监控 (AC/DC)<sup class="reference" data-ref="1">1</sup>
• 电流消耗监测
• 功率状态监控
• 温度监控<sup class="reference" data-ref="2">2</sup>

**告警功能：**
• 电源故障告警
• 过压/欠压告警 (可配置阈值)<sup class="reference" data-ref="1">1</sup>
• 过流保护告警
• 温度异常告警

**管理功能：**
• 远程电源管理<sup class="reference" data-ref="3">3</sup>
• 智能节能模式
• 电源冗余配置
• 状态日志记录

**支持协议：**
• SNMP v2c/v3 监控<sup class="reference" data-ref="4">4</sup>
• Web GUI 管理界面
• CLI 命令行接口

通过网管系统可以实时查看和管理所有电源相关参数。`,
                references: [
                    { id: 1, title: "SNPG(E)电源管理手册", url: "#" },
                    { id: 2, title: "温度监控配置指南", url: "#" },
                    { id: 3, title: "远程管理协议文档", url: "#" },
                    { id: 4, title: "SNMP配置手册", url: "#" }
                ]
            },

            'ZXCTN 6180H-A的尺寸是多少？': {
                type: 'metrics',
                content: `**ZXCTN 6180H-A 详细尺寸规格：**

**机箱尺寸：**<sup class="reference" data-ref="1">1</sup>
• 高度：1U (44.45mm)
• 宽度：482.6mm (19英寸标准机架)
• 深度：420mm

**重量规格：**
• 空载重量：约8.5kg
• 满载重量：约12kg<sup class="reference" data-ref="1">1</sup>

**安装要求：**
• 标准19英寸机架安装<sup class="reference" data-ref="2">2</sup>
• 前后通风设计
• 支持机架式和桌面式安装

**环境要求：**
• 机架深度需求：≥450mm
• 通风间隙：前后各≥50mm<sup class="reference" data-ref="3">3</sup>

**包装规格：**
• 包装尺寸：550×480×120mm
• 包装重量：约15kg`,
                references: [
                    { id: 1, title: "ZXCTN 6180H-A产品规格书", url: "#" },
                    { id: 2, title: "机架安装指南", url: "#" },
                    { id: 3, title: "环境要求技术文档", url: "#" }
                ]
            },

            'M6000-8S Plus的设备尺寸': {
                type: 'metrics',
                content: `**M6000-8S Plus 设备尺寸规格：**

**外形尺寸：**<sup class="reference" data-ref="1">1</sup>
• 高度：2U (88.9mm)
• 宽度：482.6mm (标准19英寸)
• 深度：450mm

**安装规格：**
• 机架单位：2U
• 安装方式：标准机架安装<sup class="reference" data-ref="2">2</sup>
• 支架类型：L型安装支架

**重量参数：**
• 设备净重：≤15kg
• 包装重量：≤18kg<sup class="reference" data-ref="1">1</sup>

**散热要求：**
• 风扇配置：智能调速风扇<sup class="reference" data-ref="3">3</sup>
• 散热方式：强制风冷
• 功耗：≤200W

**机架要求：**
• 需要2U机架空间
• 建议预留前后50mm通风空间<sup class="reference" data-ref="4">4</sup>`,
                references: [
                    { id: 1, title: "M6000-8S Plus产品手册", url: "#" },
                    { id: 2, title: "机架安装说明书", url: "#" },
                    { id: 3, title: "散热系统设计文档", url: "#" },
                    { id: 4, title: "机房环境要求指南", url: "#" }
                ]
            }
        };
    }

    updateSendButton(hasText) {
        this.sendBtn.disabled = !hasText;
    }

    sendMessage() {
        const question = this.questionInput.value.trim();
        if (!question) return;

        // 隐藏预设问题（第一次发送消息时）
        this.hidePresetQuestions();

        // 显示用户消息
        this.addMessage(question, 'user');
        
        // 添加到对话历史
        this.conversationHistory.push({
            role: 'user',
            content: question,
            timestamp: new Date()
        });
        
        // 清空输入框
        this.questionInput.value = '';
        this.updateSendButton(false);

        // 显示输入指示器
        this.showTypingIndicator();

        // 模拟AI回复延迟
        setTimeout(() => {
            this.hideTypingIndicator();
            this.generateResponse(question);
        }, 1000 + Math.random() * 2000);
    }

    sendPresetQuestion(question) {
        // 隐藏预设问题
        this.hidePresetQuestions();

        // 显示用户消息
        this.addMessage(question, 'user');
        
        // 添加到对话历史
        this.conversationHistory.push({
            role: 'user',
            content: question,
            timestamp: new Date()
        });
        
        // 显示输入指示器
        this.showTypingIndicator();

        // 模拟AI回复延迟
        setTimeout(() => {
            this.hideTypingIndicator();
            this.generateResponse(question);
        }, 1000 + Math.random() * 2000);
    }

    hidePresetQuestions() {
        if (!this.hasConversation) {
            this.hasConversation = true;
            this.presetQuestionsContainer.classList.add('hidden');
            // 显示新对话区域
            this.newChatSection.style.display = 'block';
        }
    }

    showPresetQuestions() {
        this.hasConversation = false;
        this.presetQuestionsContainer.classList.remove('hidden');
        // 隐藏新对话区域
        this.newChatSection.style.display = 'none';
    }

    clearChat() {
        // 清空聊天区域
        this.chatArea.innerHTML = '';
        // 清空对话历史
        this.conversationHistory = [];
        // 重新显示预设问题
        this.showPresetQuestions();
        // 重置搜索状态
        if (this.isSearchEnabled) {
            this.toggleSearch();
        }
    }

    addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        if (type === 'user') {
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = content;
            messageDiv.appendChild(messageContent);
        } else {
            // AI消息在generateResponse方法中处理，这里不需要处理
            messageDiv.textContent = content;
        }
        
        this.chatArea.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        `;
        typingDiv.id = 'typing-indicator';
        
        this.chatArea.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    generateResponse(question) {
        // 检查是否有预设回答
        let responseData = this.mockResponses[question];
        
        if (!responseData) {
            // 分类问题并生成回答，考虑对话历史
            const questionType = this.classifyQuestion(question);
            responseData = this.generateTypedResponse(question, questionType);
        } else {
            // 应用权限过滤
            responseData = this.applyPermissionFilter(responseData);
        }

        // 添加到对话历史
        this.conversationHistory.push({
            role: 'assistant',
            content: responseData.content,
            type: responseData.type,
            timestamp: new Date()
        });

        // 创建回答容器
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant';
        
        // 创建消息内容容器
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // 添加问题类型标签
        const typeLabel = document.createElement('div');
        typeLabel.className = `question-type ${responseData.type}`;
        typeLabel.textContent = this.questionTypes[responseData.type];
        messageContent.appendChild(typeLabel);

        // 添加回答内容
        const contentDiv = document.createElement('div');
        contentDiv.innerHTML = responseData.content.replace(/\n/g, '<br>');
        messageContent.appendChild(contentDiv);

        // 添加参考文档
        if (responseData.references && responseData.references.length > 0) {
            const referencesDiv = this.createReferencesSection(responseData.references);
            messageContent.appendChild(referencesDiv);
        }

        // 如果启用了联网搜索，添加搜索结果
        if (this.isSearchEnabled) {
            const searchResults = this.generateSearchResults(question);
            if (searchResults) {
                messageContent.appendChild(searchResults);
            }
        }

        messageDiv.appendChild(messageContent);

        // 添加操作按钮
        const actionsDiv = this.createMessageActions(responseData.content);
        messageDiv.appendChild(actionsDiv);

        this.chatArea.appendChild(messageDiv);
        this.setupReferenceClickHandlers(messageDiv);
        this.setupActionHandlers(messageDiv);
        this.scrollToBottom();
    }

    applyPermissionFilter(responseData) {
        // 如果回答使用旧格式（单一content），直接返回
        if (responseData.content && !responseData.knowledgeSources) {
            return responseData;
        }

        // 处理多语料源的回答
        const allowedSources = [];
        const deniedSources = [];
        
        responseData.knowledgeSources.forEach(source => {
            if (this.userPermissions.has(source.permission)) {
                allowedSources.push(source);
            } else {
                deniedSources.push(source);
            }
        });

        // 构建过滤后的内容
        let filteredContent = '';
        
        if (allowedSources.length > 0) {
            filteredContent = allowedSources.map(source => source.content).join('\n\n');
        }

        // 添加权限提示
        if (deniedSources.length > 0) {
            const deniedPermissions = [...new Set(deniedSources.map(s => s.permission))];
            const permissionNames = deniedPermissions.map(p => this.getPermissionDisplayName(p));
            
            // 如果有可显示内容，说明部分内容被屏蔽
            if (allowedSources.length > 0) {
                filteredContent += '\n\n**权限提示：**\n';
                filteredContent += `• 部分内容需要额外权限才能查看\n`;
                filteredContent += `• 请申请以下语料源的权限：${permissionNames.join('、')}\n`;
                filteredContent += `• 联系管理员获取访问权限以查看完整信息`;
            } else {
                // 如果没有可显示内容，说明全部内容被屏蔽
                filteredContent = `**权限限制提示：**\n\n`;
                filteredContent += `抱歉，您当前没有查看此问题相关内容的权限。\n\n`;
                filteredContent += `**需要的权限：**\n`;
                filteredContent += `• ${permissionNames.join('、')}\n\n`;
                filteredContent += `**申请方式：**\n`;
                filteredContent += `• 请点击下方参考文档中的【需申请权限】条目进行申请\n`;
                filteredContent += `• 或联系管理员获取访问权限`;
            }
        }

        // 合并参考文档，包含需要权限的文档
        let combinedReferences = [...(responseData.references || [])];
        
        if (deniedSources.length > 0) {
            const deniedPermissions = [...new Set(deniedSources.map(s => s.permission))];
            deniedPermissions.forEach((permission, index) => {
                const permissionName = this.getPermissionDisplayName(permission);
                combinedReferences.push({
                    id: combinedReferences.length + 1,
                    title: `${permissionName}相关文档【需申请权限】`,
                    url: "#",
                    needsPermission: true,
                    permission: permission
                });
            });
        }

        return {
            type: responseData.type,
            content: filteredContent,
            references: combinedReferences,
            hasPermissionLimitation: deniedSources.length > 0,
            deniedPermissions: deniedSources.map(s => s.permission)
        };
    }

    getPermissionDisplayName(permission) {
        const displayNames = {
            'cx66a': 'CX66A产品资料',
            'nokia_compare': 'Nokia对比分析',
            'price_sensitive': '价格敏感信息',
            'internal': '内部技术文档',
            'm6000': 'M6000产品资料',
            'zxctn': 'ZXCTN产品资料',
            'snpg': 'SNPG产品资料'
        };
        return displayNames[permission] || permission;
    }

    createReferencesSection(references) {
        const referencesDiv = document.createElement('div');
        referencesDiv.className = 'references-list';
        
        const title = document.createElement('h4');
        title.textContent = '参考文档：';
        referencesDiv.appendChild(title);

        references.forEach(ref => {
            const refItem = document.createElement('div');
            if (ref.needsPermission) {
                refItem.className = 'reference-item permission-required';
                refItem.innerHTML = `${ref.id}. <a href="#" class="permission-doc-link" data-permission="${ref.permission}">${ref.title}</a>`;
            } else {
                refItem.className = 'reference-item';
                refItem.innerHTML = `${ref.id}. <a href="${ref.url}" target="_blank">${ref.title}</a>`;
            }
            referencesDiv.appendChild(refItem);
        });

        return referencesDiv;
    }

    setupReferenceClickHandlers(messageDiv) {
        // 处理角标点击
        const references = messageDiv.querySelectorAll('.reference');
        references.forEach(ref => {
            ref.addEventListener('click', (e) => {
                e.preventDefault();
                const refId = ref.getAttribute('data-ref');
                const referencesSection = messageDiv.querySelector('.references-list');
                if (referencesSection) {
                    referencesSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    // 高亮对应的参考文档
                    const targetRef = referencesSection.querySelector(`.reference-item:nth-child(${parseInt(refId) + 1})`);
                    if (targetRef) {
                        targetRef.style.backgroundColor = '#fff3cd';
                        setTimeout(() => {
                            targetRef.style.backgroundColor = '';
                        }, 2000);
                    }
                }
            });
        });

        // 处理权限文档链接点击
        const permissionLinks = messageDiv.querySelectorAll('.permission-doc-link');
        permissionLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const permission = link.getAttribute('data-permission');
                this.showPermissionApplicationPage(permission);
            });
        });
    }

    showPermissionApplicationPage(permission) {
        const permissionName = this.getPermissionDisplayName(permission);
        
        // 创建权限申请弹窗
        const modal = document.createElement('div');
        modal.className = 'permission-modal';
        modal.innerHTML = `
            <div class="permission-modal-content">
                <div class="permission-modal-header">
                    <h3>申请权限</h3>
                    <button class="permission-modal-close">&times;</button>
                </div>
                <div class="permission-modal-body">
                    <p><strong>申请权限类型：</strong>${permissionName}</p>
                    <p><strong>权限说明：</strong>该权限允许您访问${permissionName}相关的详细技术文档和敏感信息。</p>
                    <div class="permission-form">
                        <label>申请理由：</label>
                        <textarea placeholder="请简要说明申请此权限的业务需求..." rows="3"></textarea>
                    </div>
                </div>
                <div class="permission-modal-footer">
                    <button class="btn-cancel">取消</button>
                    <button class="btn-submit">提交申请</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加事件监听
        modal.querySelector('.permission-modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.querySelector('.btn-cancel').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.querySelector('.btn-submit').addEventListener('click', () => {
            // 模拟提交申请
            alert(`权限申请已提交！\n\n申请类型：${permissionName}\n\n我们将在1-2个工作日内处理您的申请，请留意邮件通知。`);
            document.body.removeChild(modal);
        });

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    generateSearchResults(question) {
        // 模拟联网搜索结果
        const searchData = {
            '产品对比': [
                { title: "中兴通讯vs华为产品对比分析报告", snippet: "针对光传输设备的详细技术对比，包括性能、价格、服务等多维度分析..." },
                { title: "2024年光网络设备市场研究", snippet: "行业领先厂商产品竞争力分析，市场份额及技术趋势预测..." }
            ],
            '竞品分析': [
                { title: "Nokia光传输设备技术分析", snippet: "Nokia 1830系列产品技术特点、市场定位及竞争优势分析..." },
                { title: "Ciena vs 中兴通讯产品对比", snippet: "两家公司在光网络解决方案方面的技术差异和市场策略对比..." }
            ],
            '市场分析': [
                { title: "2024年光通信市场发展趋势", snippet: "全球光通信设备市场规模、增长趋势及主要驱动因素分析..." },
                { title: "5G承载网市场机会分析", snippet: "5G网络建设带来的承载网设备需求增长及市场机会..." }
            ]
        };

        // 根据问题内容匹配搜索结果
        let results = null;
        if (question.includes('对比') || question.includes('优势')) {
            results = searchData['产品对比'];
        } else if (question.includes('竞品') || question.includes('Nokia') || question.includes('华为')) {
            results = searchData['竞品分析'];
        } else if (question.includes('市场') || question.includes('趋势')) {
            results = searchData['市场分析'];
        }

        if (!results) return null;

        const searchDiv = document.createElement('div');
        searchDiv.className = 'search-results';
        
        const title = document.createElement('h4');
        title.innerHTML = '<span class="search-icon">🌐</span>联网搜索结果：';
        searchDiv.appendChild(title);

        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = 'search-item';
            resultItem.innerHTML = `
                <div class="search-title">${result.title}</div>
                <div class="search-snippet">${result.snippet}</div>
            `;
            searchDiv.appendChild(resultItem);
        });

        return searchDiv;
    }

    createMessageActions(content) {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'message-actions';
        
        const actions = [
            { id: 'copy', icon: '📋', title: '复制', action: 'copy' },
            { id: 'copy-markdown', icon: '📝', title: '复制为Markdown', action: 'copy-markdown' },
            { id: 'share', icon: '📤', title: '转发', action: 'share' },
            { id: 'like', icon: '👍', title: '点赞', action: 'like' },
            { id: 'dislike', icon: '👎', title: '点踩', action: 'dislike' }
        ];

        actions.forEach(actionData => {
            const actionBtn = document.createElement('button');
            actionBtn.className = 'action-btn';
            actionBtn.setAttribute('data-action', actionData.action);
            actionBtn.setAttribute('title', actionData.title);
            actionBtn.innerHTML = actionData.icon;
            actionsDiv.appendChild(actionBtn);
        });

        return actionsDiv;
    }

    setupActionHandlers(messageDiv) {
        const actionBtns = messageDiv.querySelectorAll('.action-btn');
        const messageContent = messageDiv.querySelector('.message-content');
        
        actionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.getAttribute('data-action');
                this.handleAction(action, messageContent, btn);
            });
        });
    }

    handleAction(action, messageContent, button) {
        const content = this.extractTextContent(messageContent);
        
        switch (action) {
            case 'copy':
                this.copyToClipboard(content);
                this.showFeedback(button, '已复制');
                break;
            case 'copy-markdown':
                const markdownContent = this.convertToMarkdown(messageContent);
                this.copyToClipboard(markdownContent);
                this.showFeedback(button, '已复制为Markdown');
                break;
            case 'share':
                this.shareContent(content);
                this.showFeedback(button, '已转发');
                break;
            case 'like':
                this.toggleLike(button);
                break;
            case 'dislike':
                this.toggleDislike(button);
                break;
        }
    }

    extractTextContent(messageContent) {
        // 创建临时元素来提取纯文本
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = messageContent.innerHTML;
        
        // 移除问题类型标签
        const typeLabel = tempDiv.querySelector('.question-type');
        if (typeLabel) typeLabel.remove();
        
        return tempDiv.textContent || tempDiv.innerText || '';
    }

    convertToMarkdown(messageContent) {
        // 简单的HTML到Markdown转换
        let markdown = this.extractTextContent(messageContent);
        
        // 转换加粗文本
        markdown = markdown.replace(/\*\*(.*?)\*\*/g, '**$1**');
        
        // 转换列表项
        markdown = markdown.replace(/•\s/g, '- ');
        
        // 添加参考文档
        const references = messageContent.querySelectorAll('.reference-item');
        if (references.length > 0) {
            markdown += '\n\n## 参考文档：\n';
            references.forEach((ref, index) => {
                const text = ref.textContent.trim();
                markdown += `${index + 1}. ${text}\n`;
            });
        }
        
        return markdown;
    }

    copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text);
        } else {
            // 备用方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            document.execCommand('copy');
            textArea.remove();
        }
    }

    shareContent(content) {
        if (navigator.share) {
            navigator.share({
                title: '产品技术问答',
                text: content
            });
        } else {
            // 备用方案：复制到剪贴板
            this.copyToClipboard(content);
        }
    }

    toggleLike(button) {
        const dislikeBtn = button.parentElement.querySelector('[data-action="dislike"]');
        
        if (button.classList.contains('active')) {
            button.classList.remove('active');
        } else {
            button.classList.add('active');
            if (dislikeBtn.classList.contains('active')) {
                dislikeBtn.classList.remove('active');
            }
        }
    }

    toggleDislike(button) {
        const likeBtn = button.parentElement.querySelector('[data-action="like"]');
        
        if (button.classList.contains('active')) {
            button.classList.remove('active');
        } else {
            button.classList.add('active');
            if (likeBtn.classList.contains('active')) {
                likeBtn.classList.remove('active');
            }
        }
    }

    showFeedback(button, message) {
        const originalTitle = button.getAttribute('title');
        button.setAttribute('title', message);
        button.style.backgroundColor = '#4CAF50';
        button.style.color = 'white';
        button.style.borderColor = '#4CAF50';
        
        setTimeout(() => {
            button.setAttribute('title', originalTitle);
            button.style.backgroundColor = '';
            button.style.color = '';
            button.style.borderColor = '';
        }, 2000);
    }

    generateTypedResponse(question, type) {
        // 检查对话历史，提供上下文相关的回答
        const contextualResponse = this.generateContextualResponse(question, type);
        if (contextualResponse) {
            return contextualResponse;
        }

        const templates = {
            'product-config': {
                content: `关于产品配置问题"${question}"：

**配置要求：**
• 请确认具体的产品型号和版本
• 建议查阅相关配置手册<sup class="reference" data-ref="1">1</sup>
• 联系技术支持获取详细配置指导

**相关文档：**
• 产品配置指南
• 技术规格说明书<sup class="reference" data-ref="2">2</sup>
• 最佳实践文档`,
                references: [
                    { id: 1, title: "产品配置手册", url: "#" },
                    { id: 2, title: "技术规格说明", url: "#" }
                ]
            },
            'solution': {
                content: `解决方案"${question}"：

**方案概述：**
• 基于客户需求的定制化解决方案<sup class="reference" data-ref="1">1</sup>
• 涵盖硬件、软件和服务的一体化方案
• 符合行业标准和最佳实践

**实施建议：**
• 详细需求分析
• 方案设计和评估<sup class="reference" data-ref="2">2</sup>
• 部署和运维支持`,
                references: [
                    { id: 1, title: "解决方案设计指南", url: "#" },
                    { id: 2, title: "实施最佳实践", url: "#" }
                ]
            },
            'command': {
                content: `功能命令"${question}"：

**操作步骤：**
• 确认设备状态和权限<sup class="reference" data-ref="1">1</sup>
• 按照操作手册执行命令
• 验证执行结果

**注意事项：**
• 建议在测试环境先验证
• 备份重要配置<sup class="reference" data-ref="2">2</sup>
• 遵循安全操作规范`,
                references: [
                    { id: 1, title: "操作手册", url: "#" },
                    { id: 2, title: "安全操作指南", url: "#" }
                ]
            },
            'metrics': {
                content: `技术指标"${question}"：

**性能参数：**
• 请参考产品技术规格书<sup class="reference" data-ref="1">1</sup>
• 相关测试报告和认证文档
• 环境要求和限制条件

**测试方法：**
• 标准测试流程<sup class="reference" data-ref="2">2</sup>
• 测试工具和环境要求
• 结果分析和解读`,
                references: [
                    { id: 1, title: "技术规格文档", url: "#" },
                    { id: 2, title: "测试方法指南", url: "#" }
                ]
            },
            'sales': {
                content: `销售策略"${question}"：

**竞争优势：**
• 技术领先性和创新能力<sup class="reference" data-ref="1">1</sup>
• 成本效益和总体拥有成本
• 本地化服务和支持能力

**市场定位：**
• 目标客户群体分析<sup class="reference" data-ref="2">2</sup>
• 差异化竞争策略
• 价值主张和收益模型`,
                references: [
                    { id: 1, title: "竞争分析报告", url: "#" },
                    { id: 2, title: "市场定位策略", url: "#" }
                ]
            },
            'knowledge': {
                content: `技术知识"${question}"：

**技术原理：**
• 相关技术标准和协议<sup class="reference" data-ref="1">1</sup>
• 工作原理和实现机制
• 应用场景和使用条件

**学习资源：**
• 技术白皮书<sup class="reference" data-ref="2">2</sup>
• 培训材料和文档
• 在线学习平台`,
                references: [
                    { id: 1, title: "技术标准文档", url: "#" },
                    { id: 2, title: "技术白皮书", url: "#" }
                ]
            },
            'lifecycle': {
                content: `生命周期管理"${question}"：

**生命周期阶段：**
• 产品发布和部署<sup class="reference" data-ref="1">1</sup>
• 运维和维护
• 升级和演进
• 退役和替换

**管理策略：**
• 定期评估和规划<sup class="reference" data-ref="2">2</sup>
• 风险管理和应急预案
• 成本优化和资源配置`,
                references: [
                    { id: 1, title: "生命周期管理指南", url: "#" },
                    { id: 2, title: "维护策略文档", url: "#" }
                ]
            },
            'case': {
                content: `成功案例"${question}"：

**项目背景：**
• 客户需求和挑战<sup class="reference" data-ref="1">1</sup>
• 解决方案设计
• 实施过程和结果

**经验总结：**
• 关键成功因素<sup class="reference" data-ref="2">2</sup>
• 最佳实践和经验分享
• 可复制的解决方案`,
                references: [
                    { id: 1, title: "项目案例报告", url: "#" },
                    { id: 2, title: "最佳实践文档", url: "#" }
                ]
            },
            'market': {
                content: `市场分析"${question}"：

**市场趋势：**
• 行业发展趋势和驱动因素<sup class="reference" data-ref="1">1</sup>
• 技术演进和标准发展
• 客户需求变化

**竞争分析：**
• 主要竞争对手分析<sup class="reference" data-ref="2">2</sup>
• 市场份额和定位
• 竞争策略和差异化`,
                references: [
                    { id: 1, title: "市场研究报告", url: "#" },
                    { id: 2, title: "竞争分析文档", url: "#" }
                ]
            }
        };

        return {
            type: type,
            content: templates[type].content,
            references: templates[type].references
        };
    }

    generateContextualResponse(question, type) {
        // 如果没有对话历史，返回null
        if (this.conversationHistory.length < 2) {
            return null;
        }

        // 获取最近的对话内容
        const recentHistory = this.conversationHistory.slice(-4); // 最近4轮对话
        const lastUserQuestion = recentHistory.filter(h => h.role === 'user').slice(-1)[0];
        const lastAssistantResponse = recentHistory.filter(h => h.role === 'assistant').slice(-1)[0];

        // 检测上下文关联关键词
        const contextKeywords = ['刚才', '刚刚', '之前', '上面', '那个', '这个', '继续', '详细', '更多', '还有', '它的', '的', '呢'];
        const hasContext = contextKeywords.some(keyword => question.includes(keyword));

        // 提取之前提到的产品
        const productMentioned = this.extractProductMentions(recentHistory);
        
        // 如果有上下文关键词或者提到了之前的产品
        if (hasContext || (productMentioned && this.isRelatedQuestion(question, productMentioned))) {
            return this.generateRealFollowUpResponse(question, type, productMentioned, lastUserQuestion, lastAssistantResponse);
        }

        return null;
    }

    isRelatedQuestion(question, product) {
        // 检查问题是否与产品相关（不需要直接包含产品名）
        const techKeywords = ['功耗', '电源', '尺寸', '价格', '成本', '安装', '配置', '接口', '端口', '性能', '参数', '规格', '温度', '协议', '支持'];
        return techKeywords.some(keyword => question.includes(keyword));
    }

    extractProductMentions(history) {
        const productPatterns = [
            'CX66A', 'ZXCTN', 'M6000', 'SNPG', 'Nokia', '1830', '6120H', '6180H'
        ];
        
        for (const entry of history.reverse()) {
            for (const product of productPatterns) {
                if (entry.content.includes(product)) {
                    return product;
                }
            }
        }
        return null;
    }

    generateRealFollowUpResponse(question, type, product, lastUserQuestion, lastAssistantResponse) {
        // 真实的产品多轮对话数据库
        const realMultiTurnData = {
            'ZXCTN 6180H-A': {
                'baseInfo': {
                    '尺寸': '高度1U(44.45mm)，宽度482.6mm，深度420mm，重量约8.5kg',
                    '功耗': '典型功耗≤180W，最大功耗≤220W，待机功耗≤15W',
                    '价格': '标准配置约15-20万元，包含基础软件许可',
                    '安装': '标准19英寸机架安装，需AC 220V双电源',
                    '接口': '8×10GE + 2×100GE，支持光口和电口',
                    '温度': '工作温度-5°C到+50°C，存储温度-40°C到+70°C'
                }
            },
            'ZXCTN 6120H-S': {
                'baseInfo': {
                    '温度': '工作温度-5°C到+50°C，扩展工作温度-10°C到+55°C',
                    '功耗': '典型功耗≤150W，最大功耗≤180W',
                    '尺寸': '高度1U(44.45mm)，宽度482.6mm，深度380mm',
                    '接口': '4×10GE + 1×40GE，全光口设计',
                    '价格': '标准配置约10-15万元'
                }
            },
            'M6000-8S Plus': {
                'baseInfo': {
                    '尺寸': '高度2U(88.9mm)，宽度482.6mm，深度450mm，重量≤15kg',
                    '功耗': '典型功耗≤200W，最大功耗≤250W',
                    '接口': '8×SFP+(10G) + 2×QSFP28(100G)，热插拔支持',
                    '价格': '标准配置约25-30万元',
                    '散热': '智能调速风扇，强制风冷'
                }
            },
            'CX66A(E)': {
                'baseInfo': {
                    '优势': '转发性能提升40%，端到端时延<1ms，支持288×10GE',
                    '价格': '比Nokia 1830低25-30%，5年TCO节省约40%',
                    '技术': '原生支持OpenFlow 1.3，NFV就绪，每Gbps功耗0.8W'
                }
            },
            'SNPG(E)': {
                'baseInfo': {
                    '电监控': '完全支持，实时电压/电流监控，过压/欠压告警',
                    '功能': '远程电源管理，智能节能模式，SNMP v2c/v3支持'
                }
            }
        };

        // 匹配产品和问题类型
        const matchedProduct = Object.keys(realMultiTurnData).find(prod => 
            product && (product.includes(prod.split(' ')[0]) || prod.includes(product))
        );

        if (matchedProduct) {
            const productData = realMultiTurnData[matchedProduct].baseInfo;
            
            // 智能匹配问题类型
            let matchedInfo = null;
            let infoType = '';
            
            if (question.includes('功耗') || question.includes('电') || question.includes('耗电')) {
                matchedInfo = productData['功耗'];
                infoType = '功耗';
            } else if (question.includes('价格') || question.includes('成本') || question.includes('费用')) {
                matchedInfo = productData['价格'];
                infoType = '价格';
            } else if (question.includes('安装') || question.includes('部署')) {
                matchedInfo = productData['安装'];
                infoType = '安装';
            } else if (question.includes('接口') || question.includes('端口')) {
                matchedInfo = productData['接口'];
                infoType = '接口';
            } else if (question.includes('温度') || question.includes('环境')) {
                matchedInfo = productData['温度'];
                infoType = '温度';
            } else if (question.includes('散热') || question.includes('风扇')) {
                matchedInfo = productData['散热'];
                infoType = '散热';
            } else if (question.includes('优势') || question.includes('特点')) {
                matchedInfo = productData['优势'];
                infoType = '优势';
            } else if (question.includes('技术') || question.includes('性能')) {
                matchedInfo = productData['技术'];
                infoType = '技术';
            } else if (question.includes('电监控') || question.includes('监控')) {
                matchedInfo = productData['电监控'];
                infoType = '电监控';
            } else if (question.includes('功能')) {
                matchedInfo = productData['功能'];
                infoType = '功能';
            }

            if (matchedInfo) {
                return {
                    type: type,
                    content: `**${matchedProduct} ${infoType}信息：**<sup class="reference" data-ref="1">1</sup>

${matchedInfo}

**与之前讨论的关联：**<sup class="reference" data-ref="2">2</sup>
• 这些参数与刚才提到的规格互相补充
• 建议综合考虑所有技术指标进行选型`,
                    references: [
                        { id: 1, title: `${matchedProduct}产品规格书`, url: "#" },
                        { id: 2, title: "技术对比分析报告", url: "#" }
                    ]
                };
            }
        }

        // 如果没有匹配到具体信息，返回通用回答
        return {
            type: type,
            content: `**基于之前讨论的补充信息：**<sup class="reference" data-ref="1">1</sup>

• 结合您刚才询问的${product || '产品'}相关信息
• 建议重点关注技术规格和实际应用需求的匹配
• 可以提供更详细的技术文档和配置建议

**专业建议：**<sup class="reference" data-ref="2">2</sup>
• 建议进行现场技术交流
• 可安排产品演示和测试`,
            references: [
                { id: 1, title: "产品技术资料", url: "#" },
                { id: 2, title: "解决方案指南", url: "#" }
            ]
        };
    }

    generateFollowUpResponse(question, type, product, lastResponse) {
        // 为不同产品提供具体的后续问答
        const productSpecificResponses = {
            'ZXCTN': {
                'metrics': {
                    '功耗': `**ZXCTN 6180H-A功耗指标：**<sup class="reference" data-ref="1">1</sup>

• 典型功耗：≤180W
• 最大功耗：≤220W
• 待机功耗：≤15W
• 节能模式：支持智能节能，功耗可降低30%

**与尺寸的综合考虑：**<sup class="reference" data-ref="2">2</sup>
• 结合之前提到的1U尺寸，功耗密度适中
• 散热设计优化，确保稳定运行`,
                    '价格': `**ZXCTN 6180H-A价格信息：**<sup class="reference" data-ref="1">1</sup>

• 标准配置：约15-20万元人民币
• 包含基础软件许可和一年技术支持
• 批量采购可享受8-9折优惠

**性价比分析：**<sup class="reference" data-ref="2">2</sup>
• 相比同类产品，成本优势明显
• TCO（总体拥有成本）较低`,
                    '安装': `**ZXCTN 6180H-A安装要求：**<sup class="reference" data-ref="1">1</sup>

• 机架安装：标准19英寸机架
• 电源要求：AC 220V，双电源冗余
• 环境要求：温度-5°C到+50°C
• 通风要求：前后各留50mm空间

**安装注意事项：**<sup class="reference" data-ref="2">2</sup>
• 基于之前提到的420mm深度，请确保机架深度足够
• 建议专业工程师现场安装调试`
                },
                'product-config': {
                    '配置': `**ZXCTN 6180H-A推荐配置：**<sup class="reference" data-ref="1">1</sup>

• 接口配置：8×10GE + 2×100GE
• 内存：32GB（可扩展至64GB）
• 存储：512GB SSD
• 冗余：双电源、双风扇

**配置优化建议：**<sup class="reference" data-ref="2">2</sup>
• 根据之前讨论的应用场景，建议选择标准配置
• 如需大容量传输，可升级到高端配置`
                }
            },
            'M6000': {
                'metrics': {
                    '功耗': `**M6000-8S Plus功耗参数：**<sup class="reference" data-ref="1">1</sup>

• 典型功耗：≤200W
• 最大功耗：≤250W
• 每端口功耗：约25W
• 能效比：业界领先水平

**与设备尺寸关联：**<sup class="reference" data-ref="2">2</sup>
• 2U设备空间，功耗控制优秀
• 散热系统采用智能调速风扇`,
                    '接口': `**M6000-8S Plus接口详情：**<sup class="reference" data-ref="1">1</sup>

• 业务端口：8×SFP+（10G）
• 上行端口：2×QSFP28（100G）
• 管理端口：1×Console + 1×Ethernet
• 扩展槽：2个预留槽位

**接口特性：**<sup class="reference" data-ref="2">2</sup>
• 支持光口和电口灵活配置
• 热插拔支持，维护便捷`
                }
            },
            'CX66A': {
                'sales': {
                    '技术优势': `**CX66A(E)技术优势详解：**<sup class="reference" data-ref="1">1</sup>

• **处理能力**：转发性能提升40%，支持Tbit级转发
• **时延控制**：端到端时延<1ms，优于Nokia方案
• **SDN支持**：原生支持OpenFlow 1.3，NFV就绪

**相比Nokia 1830的具体对比：**<sup class="reference" data-ref="2">2</sup>
• 端口密度：CX66A支持288×10GE，Nokia仅支持192×10GE
• 功耗效率：CX66A每Gbps功耗0.8W，Nokia为1.2W`,
                    '价格优势': `**CX66A(E)价格优势分析：**<sup class="reference" data-ref="1">1</sup>

• 设备采购成本：比Nokia 1830低25-30%
• 软件许可费用：采用开放架构，节省许可成本
• 运维成本：本地化服务，响应速度快

**ROI分析：**<sup class="reference" data-ref="2">2</sup>
• 基于之前讨论的技术优势，投资回报期缩短至18个月
• 5年TCO比Nokia方案节省约40%`
                }
            }
        };

        // 根据问题内容和产品匹配具体回答
        const productKey = Object.keys(productSpecificResponses).find(key => product.includes(key));
        if (productKey) {
            const productResponses = productSpecificResponses[productKey][type];
            if (productResponses) {
                const responseKey = Object.keys(productResponses).find(key => 
                    question.includes(key) || 
                    (key === '功耗' && (question.includes('功耗') || question.includes('电') || question.includes('耗电'))) ||
                    (key === '价格' && (question.includes('价格') || question.includes('成本') || question.includes('费用'))) ||
                    (key === '安装' && (question.includes('安装') || question.includes('部署') || question.includes('搭建'))) ||
                    (key === '配置' && (question.includes('配置') || question.includes('参数') || question.includes('设置'))) ||
                    (key === '接口' && (question.includes('接口') || question.includes('端口') || question.includes('连接'))) ||
                    (key === '技术优势' && (question.includes('技术') || question.includes('优势') || question.includes('特点'))) ||
                    (key === '价格优势' && (question.includes('价格') || question.includes('成本') || question.includes('优势')))
                );
                
                if (responseKey) {
                    return {
                        type: type,
                        content: productResponses[responseKey],
                        references: [
                            { id: 1, title: `${product}产品规格书`, url: "#" },
                            { id: 2, title: `${product}技术对比报告`, url: "#" }
                        ]
                    };
                }
            }
        }

        // 通用回答模板
        const followUpTemplates = {
            'metrics': `关于${product}的相关技术指标：

**技术参数补充：**<sup class="reference" data-ref="1">1</sup>
• 基于您刚才询问的${product}，这里提供相关参数
• 性能指标：满足行业标准要求
• 可靠性：MTBF≥100,000小时

**综合评估：**<sup class="reference" data-ref="2">2</sup>
• 结合之前讨论的规格，整体性能表现优异
• 建议综合考虑技术指标和应用场景`,

            'product-config': `关于${product}的配置详情：

**配置说明：**<sup class="reference" data-ref="1">1</sup>
• 基于您之前的问题，提供详细配置信息
• 支持灵活的配置选项
• 可根据实际需求定制

**配置建议：**<sup class="reference" data-ref="2">2</sup>
• 参考标准配置方案
• 建议联系技术专家进行详细配置`
        };

        return {
            type: type,
            content: followUpTemplates[type] || `关于您刚才询问的${product}相关问题"${question}"，我提供以下补充信息：

**上下文关联回答：**<sup class="reference" data-ref="1">1</sup>
• 基于您之前的问题，这里是相关的详细信息
• 建议结合之前的回答进行综合考虑
• 如需更深入的信息，请提供具体的应用场景

**建议：**
• 参考之前提到的技术要点
• 联系技术专家获取定制化建议<sup class="reference" data-ref="2">2</sup>`,
            references: [
                { id: 1, title: `${product}产品补充文档`, url: "#" },
                { id: 2, title: "上下文关联技术资料", url: "#" }
            ]
        };
    }

    generateContextFollowUp(question, type, lastUserQuestion, lastAssistantResponse) {
        // 提取关键词进行智能回答
        const contextKeywords = ['详细', '更多', '还有', '继续', '补充'];
        const isDetailRequest = contextKeywords.some(keyword => question.includes(keyword));
        
        if (isDetailRequest) {
            return {
                type: type,
                content: `**详细补充信息：**<sup class="reference" data-ref="1">1</sup>

基于您刚才的问题，我提供更详细的信息：

• **技术细节**：相关产品具有完整的技术文档支持
• **应用场景**：适用于数据中心、园区网络、运营商网络等
• **兼容性**：与主流厂商设备兼容性良好
• **维护支持**：提供7×24小时技术支持服务

**进一步了解：**<sup class="reference" data-ref="2">2</sup>
• 可提供产品演示和现场测试
• 支持个性化技术方案定制
• 有专业团队提供实施和运维指导`,
                references: [
                    { id: 1, title: "产品详细技术手册", url: "#" },
                    { id: 2, title: "技术支持服务指南", url: "#" }
                ]
            };
        }

        // 对于一般的上下文问题
        return {
            type: type,
            content: `**基于前面讨论的补充说明：**<sup class="reference" data-ref="1">1</sup>

• 结合您之前提到的需求，我建议重点关注以下方面
• 产品选型需要考虑实际的网络架构和业务需求
• 建议进行现场调研和需求分析

**专业建议：**<sup class="reference" data-ref="2">2</sup>
• 可安排技术专家进行深入沟通
• 提供定制化的解决方案建议
• 支持概念验证(PoC)测试`,
            references: [
                { id: 1, title: "需求分析指导", url: "#" },
                { id: 2, title: "解决方案案例库", url: "#" }
            ]
        };
    }

    scrollToBottom() {
        this.chatArea.scrollTop = this.chatArea.scrollHeight;
    }

    refreshPresetQuestions() {
        // 根据问题类型分类的新问题
        const newQuestionsByType = {
            'product-config': [
                "ZXCTN系列产品支持哪些接口类型？",
                "M6000设备的电源配置要求是什么？",
                "如何配置SNPG(E)的网管功能？"
            ],
            'solution': [
                "5G承载网解决方案架构设计",
                "数据中心互联DCI解决方案介绍",
                "城域网OTN解决方案优势"
            ],
            'command': [
                "如何查看设备运行状态？",
                "ZXCTN设备重启命令是什么？",
                "怎样配置链路聚合功能？"
            ],
            'metrics': [
                "ZXCTN 6180H-A的功耗指标",
                "M6000-8S Plus的传输距离",
                "设备的MTBF指标是多少？"
            ],
            'sales': [
                "相比华为产品我们有什么优势？",
                "如何制定针对运营商的销售策略？",
                "产品的ROI计算方法"
            ],
            'market': [
                "当前光通信市场的发展趋势",
                "5G网络建设对承载网的影响",
                "主要竞争对手的产品策略分析"
            ]
        };

        // 随机选择不同类型的问题
        const types = Object.keys(newQuestionsByType);
        const selectedQuestions = [];
        
        this.presetQuestions.forEach((btn, index) => {
            const randomType = types[Math.floor(Math.random() * types.length)];
            const questionsOfType = newQuestionsByType[randomType];
            const randomQuestion = questionsOfType[Math.floor(Math.random() * questionsOfType.length)];
            
            btn.textContent = randomQuestion;
            btn.setAttribute('data-question', randomQuestion);
            
            // 添加动画效果
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 100);
        });
    }
}

// 初始化聊天系统
document.addEventListener('DOMContentLoaded', () => {
    new ChatSystem();
    
    // 添加一些初始化动画
    const elements = document.querySelectorAll('.question-btn, .input-container');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            el.style.transition = 'all 0.5s ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// 添加一些额外的交互效果
document.addEventListener('DOMContentLoaded', () => {
    // 鼠标悬停效果
    const questionBtns = document.querySelectorAll('.question-btn');
    questionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', () => {
            btn.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        });
        
        btn.addEventListener('mouseleave', () => {
            btn.style.boxShadow = 'none';
        });
    });

    // 输入框焦点效果
    const questionInput = document.getElementById('questionInput');
    questionInput.addEventListener('focus', () => {
        document.querySelector('.input-container').style.transform = 'scale(1.02)';
    });
    
    questionInput.addEventListener('blur', () => {
        document.querySelector('.input-container').style.transform = 'scale(1)';
    });
});