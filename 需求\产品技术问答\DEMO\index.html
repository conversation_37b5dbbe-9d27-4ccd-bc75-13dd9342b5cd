<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品技术问答系统</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 顶部标题 -->
        <div class="header">
            <h1>您已进入营销智能伙伴</h1>
        </div>

        <!-- 欢迎文本 -->
        <div class="welcome-text">
            <p>尊敬的用户，作为您的专属营销智能伙伴，任何营销疑问我都能为您答案。尽管当前能力尚在培养，但我正不断学习成长。让我们开局向行，一起挖掘并创造无尽可能！</p>
        </div>

        <!-- 预设问题按钮 -->
        <div class="preset-questions">
            <button class="question-btn" data-question="我司CX66A(E)相比Nokia 1830 PSS-16i有哪些优势？">我司CX66A(E)相比Nokia 1830 PSS-16i有哪些优势？</button>
            <button class="question-btn" data-question="What is the operation temperature of ZXCTN 6120H-S?">What is the operation temperature of ZXCTN 6120H-S?</button>
            <button class="question-btn" data-question="SNPG(E)是否支持电监控？">SNPG(E)是否支持电监控？</button>
            <button class="question-btn" data-question="ZXCTN 6180H-A的尺寸是多少？">ZXCTN 6180H-A的尺寸是多少？</button>
            <button class="question-btn" data-question="M6000-8S Plus的设备尺寸">M6000-8S Plus的设备尺寸</button>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area" id="chatArea">
            <!-- 聊天消息将在这里显示 -->
        </div>

        <!-- 新对话按钮 -->
        <div class="new-chat-section" id="newChatSection" style="display: none;">
            <span class="clear-chat" id="clearChat">
                <span class="new-chat-icon">⊕</span> 新对话
            </span>
        </div>

        <!-- 底部输入区域 -->
        <div class="bottom-section">
            <div class="input-section">
                <div class="input-header">
                    <div class="common-qa">
                        <span class="qa-icon">🧠</span>
                        <span>常识问答</span>
                        <button id="searchToggle" class="search-toggle-header" title="联网搜索">
                            🌐 联网搜索
                        </button>
                    </div>
                </div>
                <div class="input-container">
                    <input type="text" id="questionInput" placeholder="有问题尽管问我~" />
                    <button id="sendBtn" class="send-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>
            </div>

        </div>

        <!-- 免责声明 -->
        <div class="disclaimer">
            <p>内容由AI生成，仅供参考，请勿轻信权威专业意见。《使用条款》《用户协议》《许可证使用声明》</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
