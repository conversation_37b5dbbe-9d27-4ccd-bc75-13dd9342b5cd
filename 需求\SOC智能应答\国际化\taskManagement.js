export default {
  taskManagement: {
    // 标签
    label: {
      keywords: '关键词',
      taskCode: '任务编码',
      taskName: '任务名称',
      country: '国家',
      customer: '客户',
      project: '项目',
      branch: '运营商/分支',
      dataSource: '数据源'
    },
    // 提示信息
    msg: {
      taskNameExists: '任务名称已存在，请更换',
      plsEnterKeywords: '请输入关键词(任务名称/客户名称/项目名称)',
      plsEnterTaskCode: '请输入任务编码',
      plsEnterTaskName: '请输入任务名称',
      plsSelectCountry: '请选择国家',
      plsSelectCustomer: '请选择客户',
      plsSelectProject: '请选择项目',
      plsSelectBranch: '请选择运营商/分支',
      plsSelectDataSource: '请选择数据源',
      confirmDelete: '确定要删除这个任务吗？',
      deleteSuccess: '任务删除成功',
      createSuccess: '任务创建成功',
      updateSuccess: '任务更新成功',
      copySuccess: '任务复制成功',
      formValidationError: '请检查表单填写是否正确',
      uploadSuccess: '文件上传成功',
      uploadError: '文件上传失败',
      fileTypeError: '只能上传Excel格式的文件',
      fileSizeError: '文件大小不能超过10MB',
      quickResponseSuccess: '快捷应答提交成功',
      quickResponseError: '快捷应答提交失败',
      exportSuccess: '导出成功',
      exportError: '导出失败',
      itemExport: '条目导出',
      itemImportTemplate: '条目导入模板',
      unit: '条'
    },
    // 按钮
    button: {
      query: '查询',
      reset: '重置',
      createTask: '创建任务',
      quickResponse: '快捷应答',
      response: '应答',
      edit: '编辑',
      copy: '复制',
      delete: '删除',
      save: '保存',
      cancel: '取消',
      confirm: '确定',
      submit: '提交'
    },
    // 表格列
    column: {
      serialNumber: '序号',
      taskCode: '任务编码',
      taskName: '任务名称',
      country: '国家',
      customer: '客户',
      project: '项目',
      responseProgress: '应答进度',
      satisfaction: '总满足度',
      createdBy: '创建人',
      createTime: '创建时间',
      updatedBy: '更新人',
      updateTime: '最近更新时间',
      operation: '操作'
    },
    // 弹窗
    dialog: {
      createTask: '创建任务',
      editTask: '编辑任务',
      copyTask: '复制任务',
      quickResponse: '快捷应答'
    },
    // 表单
    form: {
      taskName: '任务名称',
      country: '国家',
      groupCustomer: '集团客户简称/MTO',
      branch: '运营商/分支',
      customer: '客户',
      project: '项目',
      projectCode: '项目编码',
      projectName: '项目名称',
      customerInfo: '客户信息补充',
      dataSource: '数据源',
      responseFile: '应答条目文件',
      copyResponseResults: '复制应答结果',
      copyResponseResultsTip: '勾选后将复制原任务的应答结果，不可再导入条目文件',
      plsEnterTaskName: '请输入任务名称',
      plsSelectCountry: '请选择国家',
      plsSelectGroupCustomer: '请选择集团客户简称/MTO',
      plsSelectBranch: '请选择运营商/分支',
      plsSelectCustomer: '请选择客户',
      plsEnterProject: '请输入项目名称',
      plsEnterProjectCode: '请输入项目编码',
      plsEnterProjectName: '请输入项目名称',
      plsEnterCustomerInfo: '请输入客户信息补充',
      plsSelectDataSource: '请选择数据源',
      selectFile: '选择文件',
      fileUploadTip: '只能上传Excel文件，且不超过10MB'
    },
    // 数据源选项
    dataSource: {
      docLib: '文档库',
      projectDoc: '项目文档',
      historySoc: '历史SOC文档'
    },
    // 快捷应答
    quickResponse: {
      tips: '通过快捷应答提交的条目将自动创建个人任务并放置在个人任务区，您可以在任务管理中查看和管理这些任务。个人任务除了无法删除外，其他功能与普通任务完全相同。',
      product: '产品',
      country: '国家',
      branch: '运营商/分支',
      customer: '客户名称',
      project: '项目名称',
      itemInput: '条目输入',
      itemInputTip: '输入条目描述后，系统将自动匹配GBBS中的相关数据进行应答',
      plsSelectProduct: '请选择产品',
      plsSelectCountry: '请选择国家',
      plsSelectBranch: '请选择运营商/分支',
      plsEnterCustomer: '请输入客户名称',
      plsEnterProject: '请输入项目名称',
      plsEnterItems: '请输入条目描述'
    },
  },

    // 任务详情
    taskDetail: {
      pageTitle: '任务详情',
      taskName: '任务名称',
      groupCustomer: '集团客户简称/MTO',
      customerInfo: '客户信息补充',
      country: '国家',
      branch: '运营商/分支',
      customer: '客户',
      project: '项目',
      dataSource: '数据源',
      tabs: {
        itemManagement: '条目管理',
        dataAnalysis: '数据分析'
      },
      msg: {
        loadFailed: '加载任务详情失败'
      },
      dataAnalysis: {
        comingSoon: '数据分析功能即将上线',
        description: '该功能将为您提供详细的应答数据分析和统计报告'
      }
    },
    // 数据分析
    dataAnalysis: {
      totalItems: '总条目数',
      respondedCount: '已应答数',
      notRespondedCount: '未应答数',
      respondingCount: '应答中数',
      responseRate: '应答完成率',
      fcCount: 'FC',
      pcCount: 'PC',
      ncCount: 'NC',
      satisfactionRate: '满足度',
      productProgress: '产品维度进度',
      productName: '产品名称',
      refresh: '刷新',
      msg: {
        loadFailed: '加载数据失败'
      }
    },
    // 条目管理
    itemManagement: {
      search: {
        itemCode: '编号',
        itemDesc: '条目描述',
        product: '产品',
        status: '应答状态',
        tags: '标签',
        assignee: '应答人',
        plsEnterItemCode: '请输入编号',
        plsEnterItemDesc: '请输入条目描述',
        plsSelectProduct: '请选择产品',
        plsSelectStatus: '请选择应答状态',
        plsEnterTags: '请输入标签',
        plsSelectAssignee: '请选择指派人',
        plsSelectResponse: '请选择应答结果',
        plsEnterResponseDesc: '请输入应答描述',
        plsEnterReference: '请输入索引',
        plsSelectResponseMethod: '请选择应答方式',

      },
      buttons: {
        startResponse: '开始应答',
        batchDelete: '批量删除',
        batchAddTags: '批量添加标签',
        batchRemoveTags: '批量移除标签',
        addProduct: '添加产品',
        permissionManagement: '授权',
        export: '导出',
        singleAdd: '单条录入',
        batchImport: '批量导入',
        filter: '筛选',
        more: '更多',
        query: '查询',
        reset: '重置',
        save: '保存',
        cancel: '取消',
        confirm: '确定',
        submit: '提交',
        viewReference: '查看',
        editDetail: '编辑详情',
        aiResponse: 'AI应答',
        edit: '编辑',
        delete: '删除',
        selectAll: '全选',
        viewSelected: '查看已选',
        columnSetting: '列设置'
      },
      columns: {
        itemCode: '编号',
        itemDesc: '条目描述',
        tags: '标签',
        status: '应答状态',
        product: '产品',
        response: '满足度',
        assignee: '应答人',
        responseMethod: '应答方式',
        responseDesc: '应答说明',
        source: '语料源',
        reference: '索引',
        remark: '备注',
        lastUpdatedBy: '最近更新人',
        lastUpdatedDate: '最近更新时间',
        operation: '操作'
      },
      responseMethodOptions:{
        MANUAL: '手工',
        AI: 'AI'
      },
      status: {
        notResponded: '未应答',
        responding: '应答中',
        responded: '已应答',
        answerFailed: '应答失败'
      },
      form: {
        itemCode: '编号',
        itemDesc: '条目描述',
        product: '产品',
        productPath: '产品路径',
        assignee: '应答人',
        autoResponse: '自动应答',
        remark: '备注',
        overwriteOnDuplicate: '重复时覆盖',
        plsEnterItemCode: '请输入编号',
        plsEnterItemDesc: '请输入条目描述',
        plsSelectProduct: '请选择产品',
        plsSelectProductPath: '选择产品后带出',
        plsSelectAssignee: '请选择指派人',
        plsEnterRemark: '请输入备注',
        enableAutoResponse: '开启自动应答',
        enableOverwrite: '重复时覆盖原条目'
      },
      dialog: {
        singleAdd: '单条录入',
        editItem: '编辑条目',
        batchImport: '批量导入',
        batchAddTags: '批量添加标签',
        batchRemoveTags: '批量移除标签'
      },
      addProduct: {
        title: '新增产品',
        product: '产品',
        plsSelectProduct: '请选择或输入产品',
        tipNoDuplicate: '不能选择已有产品',
        confirmTip: '拟将产品调整至{product}，请确认是否继续？',
        confirmAll: '将对所有条目新增产品，请确认是否继续？',
        success: '新增产品成功',
        failed: '新增产品失败'
      },
      import: {
        tips: '请选择要导入的Excel文件，确保文件格式正确且包含必要字段',
        file: '导入文件',
        selectFile: '选择文件',
        fileTypeTip: '只能上传Excel文件，且不超过10MB',
        downloadTemplate: '下载导入模板'
      },
      tags: {
        newTags: '新标签',
        removeTags: '移除标签',
        plsEnterTags: '请输入或选择标签',
        plsSelectRemoveTags: '请选择要移除的标签'
      },
      msg: {
        loadFailed: '加载条目列表失败',
        saveSuccess: '保存成功',
        saveFailed: '保存失败',
        deleteSuccess: '删除成功',
        deleteFailed: '删除失败',
        importSuccess: '导入成功',
        importFailed: '导入失败',
        uploadSuccess: '文件上传成功',
        uploadError: '文件上传失败',
        fileTypeError: '只能上传Excel格式的文件',
        confirmStartAllResponse: '未选择条目，是否对所有条目开始应答？',
        startResponseSuccess: '开始应答成功',
        startResponseFailed: '开始应答失败',
        confirmBatchDelete: '将对所有条目进行批量删除操作，请确认是否继续？',
        confirmDelete: '确定要删除该条目吗？',
        confirmDeleteSelected: '确定要删除已选条目吗？',
        plsSelectTags: '请选择标签',
        addTagsSuccess: '添加标签成功',
        addTagsFailed: '添加标签失败',
        confirmBatchAddTagsAll: '将对所有条目进行批量添加标签操作，请确认是否继续？',
        removeTagsSuccess: '移除标签成功',
        removeTagsFailed: '移除标签失败',
        confirmBatchRemoveTagsAll: '将对所有条目进行批量移除标签操作，请确认是否继续？',
        aiResponseSuccess: 'AI应答成功',
        aiResponseFailed: 'AI应答失败',
        unit: '条'
      },
      permission: {
        title: '授权',
        mode: '模式',
        assign: '指派应答',
        readonly: '授权只读',
        operation: '操作',
        add: '添加只读',
        remove: '移除只读',
        users: '人员',
        plsSelectUsers: '请选择人员',
        tipAssignAll: '未勾选条目将默认对全部条目执行指派',
        tipReadonly: '授权只读无需勾选条目，可直接对只读人员进行添加或删除',
        confirmAssignAll: '请确认是否将所有条目指派给{users}？',
        assignSuccess: '指派成功',
        assignFailed: '指派失败',
        readonlySuccess: '只读权限更新成功',
        readonlyFailed: '只读权限更新失败'
      }
    },
    // 产品树选择器
    productTreeSelect: {
      placeholder: '请选择产品',
      noData: '暂无数据',
      loadError: '加载产品数据失败',
      searchError: '搜索产品失败',
      searching: '搜索中...'
    },
    // 条目详情
    itemDetail: {
      backToTask: '返回任务',
      itemDetail: '条目详情',
      basicInfo: '基本信息',
      operationHistory: '操作历史',
      save: '保存',
      aiResponse: 'AI应答',
      edit: '编辑',
      cancelEdit: '取消编辑',
      back: '返回',
      form: {
        itemCode: '编号',
        itemDesc: '条目描述',
        product: '产品',
        assignee: '应答人',
        tags: '标签',
        status: '状态',
        responseResult: '应答结果',
        responseDesc: '应答说明',
        reference: '索引',
        remark: '备注',
        plsEnterItemCode: '请输入编号',
        plsEnterItemDesc: '请输入条目描述',
        plsSelectProduct: '请选择产品',
        plsSelectAssignee: '请选择指派人',
        plsEnterTags: '请输入标签',
        plsSelectResponseResult: '请选择满足度',
        plsEnterResponseDesc: '请输入应答说明',
        plsEnterReference: '请输入索引',
        plsEnterRemark: '请输入备注'
      },
      msg: {
        loadFailed: '加载条目详情失败',
        saveSuccess: '保存成功',
        saveFailed: '保存失败',
        aiResponseSuccess: 'AI应答成功',
        aiResponseFailed: 'AI应答失败'
      }
    }
}; 
