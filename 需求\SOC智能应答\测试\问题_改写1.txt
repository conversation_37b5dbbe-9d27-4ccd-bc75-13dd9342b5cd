Project Overview
TWA has planned to implement a new long-haul transport network to address its expanding bandwidth requirements. Bidders must provide cutting-edge CDF-based DWDM/OTN solutions to meet current and future bandwidth demands while migrating existing 100G and multiple 10G services to the new infrastructure.
General Requirements
As a turnkey RFP, bidders shall deliver a complete network solution according to the functional and technical specifications outlined in the RFP. Survey, installation, testing, and commissioning shall be the bidder's responsibility.
The submitted BoQ must align with requirements and specifications. Any equipment, materials, software, licenses, etc., omitted from the bidder's BoQ/proposal that are reasonably implied and necessary for completion, effective performance, and commissioning of the transport network shall be considered included in the contract price.
Bidders must arrange all hardware and software to migrate existing services according to the scope of work defined in the RFP.
A user-friendly GUI-based high availability network management system with geographical redundancy is required for:
Network Operations, Administration, and Maintenance
Fault Management
Configuration Management
Performance Management
Predictive Maintenance
All software and licenses shall be perpetual. Bidders must include all relevant software and firmware licenses in their offer, including but not limited to interface licenses and complete NMS feature sets.
Bidders must propose a comprehensive NMS solution including software and hardware.
Bidders must design the new NMS to migrate any existing vendor footprint in TWA's network by merging instances, resources, and licenses without additional cost to the existing network.
Evaluation will be based on TCO. Bidders are required to quote expansion and O&M costs for the next five years.
Functions and Features
The proposed solution and products should represent the latest platform technology.
The platform must comply with the latest relevant standards defined by ITU-T, MEF, IETF, and IEEE.
Bidders must propose a completely new optical plane; existing/available subracks/racks for client services cannot be reused.
The proposed solution shall implement ROADM CDF on the optical layer and preferably hybrid (SDH/Packet/ODU) based cross-connect on the electrical layer. Cross-connection capacity shall be shared among all three service types.
The solution must be based on separate line cards and tributary card interfaces for cross-connection on the blade instead of passive protection cards.
The solution must be free of passive cards in both electrical and optical layers, with all hardware implemented on blades or sleds within the same shelves.
Electrical and optical layers must be segregated in different shelves to ensure no single point of failure in end-to-end signal flow at both single wavelength and multiplexed signal levels.
The proposed solution must be designed for future electrical-based GMPLS/ASON expansion, enabling the same optical layer to support a hybrid ASON network with WSON and OTN ASON functionality without additional hardware changes.
The proposed solution shall provide a minimum capacity of 9.6Tb/s @50GHz spacing per channel.
Bidder-proposed equipment/boards should support flexible mapping technology to enable adjustable 1Ge~100G bandwidth mapping on a single wavelength.
The proposed solution's electrical and optical layers must support the latest Open-API for third-party integrations.
The proposed solution must support alien wavelength switching and rerouting on GMPLS/ASON networks.
The proposed solution should preferably support multi-function transponders usable in both transponder and muxponder scenarios.
Bidders shall ensure high network availability with redundant main cards including supervisory channels, network cards, line cards, and power supplies. Card failures, additions, and removals should not affect services. All interface cards, redundant control cards, power supply modules, and fans in the proposed equipment shall be hot-swappable.
Bidders shall provide protection switching for single or multiplexed signal degradation conditions.
The system shall ensure protection switching to guarantee switching times less than 50ms for failures such as fiber cuts, line card/transponder failures, and cross-connect card failures in the network.
The solution shall strictly avoid any service cascading.
ROADMs proposed in the solution should operate in route-and-select manner and support wavelength filtering and cross-connects. Any phenomena adhering to multicast or broadcast on the add-drop side will not be acceptable.
The proposed solution must support nested protection scenarios for OTS and OMS links within ASON networks without issues, with guaranteed switching ≤50ms.
Bidders must provide all cards with pluggable optical interfaces as mandatory for clients and preferable for line optics.
The proposed equipment shall have built-in (no separate card) OTDR hardware and software features to monitor fiber break distances on trunk links between stations, viewable from NMS with traces saved in the NMS database for offline viewing.
All equipment cards shall be "hot-swappable" "plug-in" types, capable of being secured at the front of the rack to prevent accidental removal.
Unit insertion or removal while equipment is powered on shall not cause damage to the unit or equipment (hot-swappable).
Equipment shall have protection against reverse DC input polarity.
Bidders shall provide dual power supply provisions in the equipment.
All supplied equipment shall operate under the following conditions:
Temperature: 0°C to 45°C
Maximum Relative Humidity: 90%
Power: Redundant, field-replaceable power input modules
Operational input voltage: -40V to -75Vdc
The proposed solution shall support L-band for future expansion.
The proposed solution shall provide minimum 200G wavelengths upgradeable to 400G in multiples of 100G profiles without additional hardware changes. Bidders must provide wavelength and spectrum utilization details for the proposed solution.
Services will be mapped to electrical and optical layers according to the traffic matrix.
The network must be capable of GMPLS/ASON-based self-healing without service interruption (within 50msec) for any network failure until a single fiber path remains available between source and destination.
Protection must be guaranteed to maintain service availability without errors until one physical path remains available between source and destination.
Dynamic GMPLS-based ASON protection shall be proposed on optical and electrical layers with diamond (1+1) level SLA within protection and restoration limits. ASON on optical and electrical layers shall be mutually exclusive.
Bidders must ensure ASON network design with dynamic GMPLS (restoration route automatically calculated/selected) and static GMPLS options (route manually selected via NMS) without limitations.
Bidders must dimension cross-connect and line bandwidth resources according to the traffic matrix and its protection and restoration limits. Bidders must share link-wise line bandwidth resources and site-wise cross-connect resource occupancy.
Line bandwidth and cross-connect resources for protection and restoration against each service shall be dimensioned as dedicated and not shared.
Bidders must provide networks with the following parameters:
Network topology in Annexure C
Link distances given in Annexure A
Fiber attenuation shall be calculated as 0.33db/Km as in Annexure A
Fiber type is G652D
5db link margin on each segment link budget on top of insertion loss mentioned in Annexure A
5db OSNR margin above end-to-end OSNR threshold (for all available routes considering service reroutes)
Acceptance criteria mentioned in Annexure D
Bidders shall ensure high availability of the proposed solution. All interface cards, redundant control cards, power supply modules, and fans in the proposed equipment shall be hot-swappable.
Bidders must propose solutions with the least possible OLA sites while keeping design parameters intact. OLAs can only be selected from available sites given in the topology.
OLA sites should provide compact chassis to save power and space, with OLA site optical layer boards/subracks preferably compatible with ROADM sites to reduce spare and maintenance complexity.
The optical layer shall preferably be based on EDFA amplifiers only.
Bidders must ensure cards are distributed among different racks/subracks (except OTN) to avoid single points of failure excluding OLA.
Maximum restoration time for any service (except services among Karachi, Lahore, Faisalabad, and Islamabad) shall not exceed 30 seconds.
Maximum restoration time for services among Karachi, Lahore, Faisalabad, and Islamabad shall not exceed 60 seconds.
The proposed solution shall support latency monitoring and latency-aware routing functions for latency-sensitive applications/circuits.
Bidders shall not cap the proposed capacity; the proposed 100G/200G/300G/400G data rate capacity shall be fully available without limitations.
The solution shall strictly avoid any service cascading.
Bidders must provide all client interfaces with optical modules (10Km), with the same cards preferably supporting 40Km optical modules.
Ethernet services for FE, GE, and 10GE (preferably) traffic shall be mapped through Packet/Ethernet cross-connect.
For all electrical layer service sets, line cards shall be universal with respect to cross-connection.
Bidders must provide all client boards with pluggable optical interfaces as mandatory; line optics are preferable.
1+1 high availability geo-redundant NMS solution shall be part of the solution; bidders must share respective requirements.
In addition to NMS, TWA is planning super controller and transport domain controller on its private cloud in the future, and the solution is expected to meet hardware and software resources provided by hosted cloud environments. Vendors need to clearly mention hardware platform and virtualization environment resource requirements like VMs, vCPUs, memory, and storage in the main solution.
Bidders must provide necessary support to TWA (if required) for SDN domain controller and development of web GUI interface for services such as BoD (Bandwidth on Demand).
If proprietary software is used at database layer, OS layer, virtualization layer, or separate hardware is required, it must be clearly mentioned in the solution, and all future requirements including expansions, developments, and licensing prices must be provided with the main solution, or it will be provided free of charge in the future.
All equipment shall be offered with 3-year warranty. Bidders must provide operation and maintenance support as per Annexure B; detailed operation and maintenance proposals shall be part of the submission.
Bidders shall ensure smooth interconnection on standard interfaces with other networks.
All proposed lambdas shall be tunable.
The proposed equipment shall have features to monitor wavelength power and OSNR or Pre-FEC BER at each site, each degree, in receive directions with built-in hardware and software, viewable via NMS.
The proposed equipment shall have built-in OTDR with dynamic range ≥40db.
OTDR accuracy shall be ±10 meters when compared with offline OTDR.
Channel OSNR and power shall be monitored on all NEs.
OSNR accuracy shall be ±2dB when compared with external OSA.
In addition to transmission equipment and NMS, bidders shall supply all necessary power cables, breakers, fiber cables, connectors, attenuators, etc., required for bringing the supplied equipment into service.
The solution should support the span power budget of the proposed platform for G652D fiber for single and multi-span applications.
Client patch panels shall be part of the solution. Bidders must ensure that half of the client ports remain spare after accommodating the traffic matrix.
Power and grounding cables shall be dimensioned considering fully loaded rack capacity. Bidders should assume 30~50m length from rectifier to equipment.
The proposed equipment shall be rack-mounting type with proper housing for air ventilation and access control.
The proposed equipment shall be of modular construction for ease of operation and maintenance. Replacement/addition of units and parts shall be done from the front side of the rack.
All equipment cards shall be "hot-swappable" "plug-in" type.
In addition to network alarm reporting, proposed equipment should support extension of "housekeeping" alarms related to power and environment as dry contact or auxiliary interfaces. Bidders must specify the number of housekeeping alarms supported by the proposed equipment.
Bidders shall provide dual power supply provisions in the equipment.
Heat flow mechanism shall be "front to top" or "front to back."
Proposed equipment shall operate under the following conditions:
Temperature: 0°C to 45°C
Maximum Relative Humidity: 95%
Operational input voltage: -40V to -75VDC
The proposed ROADM shall support minimum 9 degrees.
The proposed ROADM shall have the ability to add/drop wavelengths without impacting the performance of other wavelengths.
The proposed ROADM shall have per-channel automatic power adjustment to equalize all DWDM channels and shall be configurable by NMS.
The control plane shall provide automatic network topology and NE discovery.
The control plane shall provide protection and dynamic restoration of the DWDM network in case of failures.
The control plane shall be aware of optical network topology, optical component functional models, node models, node configuration, and fiber characteristics.
The control plane shall allow users to specify path constraints: include or exclude nodes and links in working and protection paths and protect DWDM circuits.
The control plane shall support bidirectional circuits.
The system shall support revertive, non-revertive, and manual protection switching of circuits.
The proposed system shall have gain flattening capability throughout the operational spectrum to keep the spectrum flat.
The proposed cross-connect equipment shall be single chassis-based. The proposed equipment shall be able to extend configuration by multiple shelves managed under a single NE.
The proposed system shall have automatic laser shutdown features in case of optical path breaks and manual laser shutdown features for O&M functions.
The proposed system shall comply with ITU-T G664 (10/12 & 12/14) recommendations for optical safety procedures and requirements for optical transport systems.
The proposed system shall comply with IEC 60825-1 recommendations for safety of laser products-Part 1: Equipment classification, requirements, and user's guide.
The proposed system shall comply with IEC 60825-2 recommendations for safety of laser products-Part 2: Safety of optical fiber communication systems.
The proposed equipment shall be protected against electrostatic discharge.
The proposed system shall comply with electromagnetic emission limits as per EN55022:2010 and EN55024:2010.
The proposed DWDM equipment shall comply with other requirements: Optical Safety: IEC60825, Electrical Safety: IEC and UL 60950; Climatic ETSI 300-019-1-3: Class 31e/32 Transport: ETSI 300-019-1-2: Class 23 Storage: ETSI 300-019-1-1: Class 12
Ethernet Interfaces: The equipment shall have
Ethernet Interfaces: The equipment shall have:
SFP, SFP/SFP+, CFP/CFP2/CFP8/OSFP/QSFP-DD/QSFP28/QSFP56 interfaces (single mode) for LAN/WAN against 10/100/200/400 Gigabit Ethernet Interfaces
FlexE and FlexO to provide granularity
The proposed equipment shall have features to monitor wavelength power and OSNR or Pre-FEC BER at each site, each degree, in receive directions with built-in hardware and software, viewable via NMS.
The proposed equipment shall support automatic power adjustment of all active wavelengths in the equipment.
The proposed equipment shall have automatic channel power adjustment function according to wavelength power, wavelength OSNR, and wavelength BER, and shall also be manually adjustable via NMS.
Bidders shall provide customized software tools/planning tools for network performance in addition to NMS applications for planning purposes (especially resource utilization) with perpetual licenses.
The software tool/planning tool shall be the latest version and shall be installed on any laptop or computer.
Bidders shall provide complete network design using the planning tool. The necessary planning files for designing the network shall be submitted along with this software/planning tool.
Bidders shall provide references for successful deployment of electrical and optical ASON networks worldwide.
Spare Parts Management
Solution providers shall guarantee availability of spare components, units, subunits, and compatible equipment for a minimum period of ten (10) years from equipment commissioning date.
Five percent spares must be part of TCO. For every critical spare (i.e., Transponder/Muxponder or any other service-affecting board), regional availability at every defined Regional Maintenance Center is required (minimum 7 spare boards of each type must be provided, placed at 7 designated regions and maintained by solution provider).
Migration Strategy
Procedural methods for undertaking the migration
Maintenance windows (MW) required in alignment with proposed PIP
Roadmap
Software and hardware roadmap with EOS details for at least 10 years
Detailed product descriptions with datasheets
Bills of Materials
Site-wise itemized BoM covering hardware, software, services, training, and local materials (same shall be replicated as BoQ in commercial offer)
BOQ-TCO format duly completed
Operation and Maintenance Proposal
Success Stories
Success stories with operator name, product name, commissioning year, network type, protection schemes, and contact details
Project Implementation Plan
Detailed PIP
Training Proposal
Foreign: for 8 personnel in 2 batches
Local: Islamabad and Karachi for 15 personnel at each city in 2 batches
Proposed equipment should support electrical cross-connect architecture for service grooming
Proposed equipment should support ROADM architecture for optical layer
Proposed equipment should support minimum 100G per channel capacity
Optical layer should be designed for 96CHs to ensure future expansion capability
Proposed network should be DCM-free, coherent architecture
Proposed equipment should support electrical ASON
Proposed equipment should support WSON
Bidders must clearly specify total NE (Network Element) count for a single ASON domain
Proposed equipment should support beyond 100G bandwidth capacities (i.e., 400G single carrier) for future upgrade
Proposed equipment should support universal cross-connect boards with centralized capability to support SDH, PKT, and OTN grooming
Proposed equipment should support programmable line boards
Cross-connect boards should support VC-level grooming
Per-slot capacity of proposed equipment should not be less than 400G and should be upgradeable to 1T without chassis replacement
Proposed equipment, 100G single line boards should have support for SDH, PKT, and OTN features
Proposed equipment should support L2 PKT features (MPLS-TP) and OTN features for client and line-side service boards
Proposed equipment should support Colorless, Directionless, Contention-less, and Grid-less (CDCG) ROADM. However, for initial scope, Colorless and Directionless ROADM capable of upgrade to Contention-less and grid-less with minimum or no hardware change can be proposed
Proposed equipment should have built-in OTDR and OSNR monitoring features
End-to-end service latency measurement function should be available in NMS features
Suppliers should provide 4 client terminals with initial license for 10 clients, each having multiple login-based capabilities
Proposed equipment should support SDN without any hardware change for future upgrade
Proposed equipment shall have minimum life cycle of 10 years from purchase date for network expansion (available for purchase) and end of support shall not be less than 15 years (spares availability)
Proposed network shall be designed with additional 3dB per link loss margin on each link provided losses
Proposed network shall be configured with minimum 4dB OSNR margin from minimum threshold of proposed line board
Minimum OSNR tolerance, CD and PMD tolerance of proposed line boards should be provided along with solution
Proposed solution should be quoted with electrical cross-connected sub-racks at all ADD/DROP locations and ring intersecting sites as defined in provided topology
Proposed cross-connect boards should have centralized matrix for SDH, PKT, and OTN service grooming
Cross-connect boards should be provided with full capacity licenses
After configuring current solution required boards, proposed electrical sub-rack should have enough free slots to support at least 2x 100G more lambda additions including clients and line boards
L2 supported PKT client boards with GE and 10GE optical interfaces, preferably in single board, shall be proposed
OTN client board supporting multi-rate interfaces for transparent GE, 10GE, and STM-N from ODU0~ODU1~ODU2 shall be proposed
Proposed 100G line board should support SDH, PKT, and OTN services
Proposed solution should have MPLS-TP function to support PKT service requirements
Proposed solution should support electrical ASON for smaller granularity services (i.e., FE, GE, 10GE, and STM-N) for 100G shared capacity lambda between 5 add/drop cities as provided in traffic matrix
Proposed solution should be designed to have WSON switching capability from day one for point-to-point express lambdas between major cities (i.e., between Islamabad, Lahore, and Karachi)
Bidders should clearly state proposed ASON SLA
All ADD/DROP locations, including all ring intersection nodes (having more than 2 directions), should be configured as directionless ROADMs with minimum 2 local dimensions. ADD/DROP locations having 2 directions can be configured as directional ROADM
For 100G shared capacity lambdas, service protection should be designed to protect and reroute ring-wise traffic as defined in provided topology
Traffic should be protected against multiple fiber cuts and should be rerouted to any available path. Protection can be realized using ASON (preferred) or any PKT protection scheme for 3rd degree protection (for PKT services)
Service switching time shall be less than 50ms. Bidders need to clearly mention reroute time for ASON services
Proposed solution should support in-service online OTDR features
Proposed solution should support online OSNR monitoring function
Detailed HLD, proposed equipment documentation, and site-wise typical and maximum power requirements need to be provided along with BOQ
10% spares should be provided separately for each phase. Critical board quantity per region (i.e., North, Center, and South) shall not be less than 2. Critical boards include but are not limited to cross-connect boards, client boards, line boards, WSS, amplifiers, and add/drop components or any items whose failure can cause service outage. Minimum 1 sub-rack electrical and optical should be provided per region as spare
BOQ defined in traffic matrix as planning pool needs to be provided with all required licenses
Please see instructions in Section 4 and respond if any instructions are not met
Please refer to SOW for content
Please refer to SOW for content
Please refer to SOW for content
Please refer to SOW for content
Please refer to SOW for content
Please refer to SOW for content
Bidders should provide following foreign training for Wateen technical staff:
Network Planning/Configuration & Optimization: 2 persons
Operations & Maintenance: 6 persons
Bidders should provide local training of O&M for 12 persons for Wateen staff
In addition, bidders should provide all technical documentation of product, line/configuration, and troubleshooting manuals
Bidders shall be responsible for complete end-to-end boarding, lodging, and other training-related expenses
Bidders shall be responsible to provide three (3) year FOC warranty including R&R. Warranty would start from PAC issuance date
Vendors should cover free-of-cost software bug fixes, software/patch upgrades to cater any software/hardware incompatibility issues arising in the network
Bidders shall charge no fee for any equipment/network/NMS software upgrade including charges for annual licenses of main software/unique feature upgrade
Any future upgrade in equipment/network software/feature to be covered free of cost
Any future upgrade in NMS software to be covered as free-of-cost
Bidders shall quote service migration cost for each phase as outlined above from existing network (3 *10G SDH/DWDM) separately
Bidders shall provide Level-II & Level-III technical support in case of outstanding issues in network
Proposed equipment shall have minimum life cycle of 10 years from purchase date for network expansion (available for purchase) and end of support shall not be less than 15 years (spares availability)
10% of BOQ quantity to be proposed as spares for O&M purposes and availability of redundant hardware in case of failure. These spares are separate from planning pool as described in traffic matrix. Critical boards include but are not limited to cross-connect boards, client boards, line boards, WSS, amplifiers, and add/drop components or any items whose failure can cause service outage. Minimum 1 sub-rack electrical and optical should be provided per region as spare
Technical offer and commercial offer to be submitted separately
Commercial offer should be aligned with technical offer. Clear phase-wise submission is required for both technical and commercial offers
TDM boards for STM-N, 100G client boards, programmable line boards, and related licenses not part of solution but may be needed later should be provided in optional item list
Express lambda solution along with per lambda price for KHI-ISB, KHI-LHR, and LHR-ISB should be quoted
Unit site addition cost, along with required BOQ including but not limited to electrical sub-rack, line and client boards, auxiliary boards, and related licenses should be quoted. 1+1 client boards along with 2 x 100G line board can be taken as standard configuration for reference quotation
Site conversion cost from OLA to ROADM (2-direction) along with BOQ including additional amplifiers, WSS, MUX/DEMUX, optical sub-rack, and related licenses
Service cost should be considered along with equipment
Any item including hardware and licenses required for complete functionality of quoted solution, if not captured, will be bidder's responsibility and shall be provided FOC
Wateen can order phase-wise BOQ or as a whole depending upon requirement. However, vendor needs to provide quotation for all phases including WSON and Ring#9. Prices for all scopes and UPL will be locked with successful bidder
VLAN switching
MAC/trunk sharing (point to multipoint)
Bridging (Ethernet LAN)
LAG
DLAG/MC-LAG
Cross-board LAG support (LAG between 2 L2 client boards)
LAG node balancing between boards
LPT
Real-time monitoring (packets in/out, errors related to framing/collisions/drop events), etc.
VLAN nesting/stacking (QinQ 802.1ad)
MPLS features
QoS policy/traffic shaping (CAR/CoS/H-QoS)
Real-time utilization (graphs & latency monitoring)
Pseudo wire tunneling
Benchmark test suites (RFC2544, etc.)
Maximum number of trunk ports supported in L2 Ethernet switch/service board
BW limit below 100mb. Lowest possible BW restriction
MS-PW+Tunnel APS
PW APS+Tunnel APS
Dual homing protection
OAM
With future SDN, features like bandwidth on demand support
Online service adjustment
SCO is planning to deploy/upgrade its data network applications to an IP Next Generation Network. The network is envisioned to provide and support IP-based multiple services such as voice, video, data, etc. The intended network is envisioned for mission-critical applications as well to offer L2, L3, MPLS, VPLS, L2VPN, and L3VPN services. SCO network design is based on core and aggregation sites. Core sites would provide core routing, Internet peering, and subscriber management services. Whereas aggregation sites would provide services to transport traffic towards core sites. The objective of this document is to define the scope of work, technical specifications, and standards or equipment and services required for deployment of the SCO network. The detail of architecture is covered below.
For execution of the works, bidders shall undertake to supply, install, and commission all hardware and software including equipment, installation material, interfacing units, mounting cabinets, power supply, etc., (as detailed in specifications) which shall be necessary for a complete functioning entity. Under scope of work which may not have specifically been mentioned in BOQ, but which are necessary to meet specifications shall be provided by bidders without any extra charges. In case of doubts, kindly get written clarifications from SCO. Following items shall be required for execution of the work.
Hardware as per BOQs including network equipment, equipment cabinets, etc.
Software/operating system of proposed hardware
Services (HLD/LLD, installation, commissioning, support, etc.)
Documentation both in soft and hard copies
Tools, jigs, fixtures, labeling tools for cables & appliances for installation
Site preparation (power cabling from mains, rack, equipment installation, circuit breakers, power sockets, earth testing, trays for patch cords, etc.) In addition, surveys shall be the responsibility of successful bidders. Site preparation is applicable to core and aggregation boxes
The details of network architecture are as under:
Two main core sites: SCO HQ and Denyore are Internet peering points and subscriber management/termination would be present. Further, these sites host a server segment and connectivity with core network elements (MSC, HLR, VLR, etc.). 2G, 3G, and 4G sites would also be homed on these core sites. These core sites also act as aggregation for remote & metro locations like Chillas, Skardu, Gilgit, and Karimabad. Internet peering would be established on both of these core sites in active/backup fashion. The core sites would provide termination of 10Gs and 100Gs
Two aggregation sites: Muzaffarabad and Mirpur provide aggregation connectivity to remote metro locations on 1G/10G, 2G/3G/4G sites and uplinked with main core sites over multiple of 10G or 100G
4.1 Option1 (Priority 1), Complete solution is offered using IPoDWDM technology
In this case, vendors need to provide solution based on IPoDWDM technology if technology is available with bidders. Vendors can choose 3rd party OEMs for product delivery. Vendors need to deploy IPoDWDM nodes at all mentioned core locations and intermittent locations where nodes are required to mitigate attenuation/distance issues. Bidders should provide design of 100G network including link redundancy & amplifier requirement. Bidders may or may not utilize the multiplexer of existing system. If multiplexer, DCM, or amplifier/booster interfaces are required to be inducted in presently deployed ZTE and Huawei network, then it must be included in design. Core routers at 2 x locations should be linked via 100G with IPTX location (HQ SCO rwp). Aggregate and other servicing locations will be connected via 100G (scalable in future) over IPoDWDM, DWDM, or metro Ethernet.
In this scenario, bidders are required to offer IP core as per provided IP core requirements in this RFP while for transmission purposes bidders need to upgrade existing DWDM network or may offer dedicated new OTN/DWDM to meet 100G requirements. This 100G will be an add-on requirement over present 60G bandwidth already available in network (i.e., 100G+60G=160G)
Regarding proposals based on existing DWDM network, bidders need to coordinate with relevant vendor/s at their own to get competitive cost and required solution. Bidders are also responsible for sourcing, deployment, and post-deployment support as per requirements mentioned in this RFP and its related annexures.
SCO will not be liable for integration/upgradation on existing DWDM with incumbent vendor.
Using option 2, bidders need to provide IP core cost and transport cost separately.
IP Core Cost + Existing DWDM upgrade / New OTN/DWDM cost = Total Solution cost.
Current SCO DWDM network is running on multiple of 10G lambda with maximum interface limit/bandwidth of 10G. To support 100G interface on DWDM, hardware upgradation will be required on DWDM side.
Annexure A: Current DWDM hardware detail
Currently, IT network and all its services are running over 10G ZTE DWDM links along with Metro Ethernet connectivity. In future, SCO plans to connect all core and access sites to be over 100G IPoDWDM network.
IPoDWDM technology will have dedicated nodes deployment (instead of upgrading existing DWDM technology) on separate dark fiber. IPoDWDM nodes should be deployed at core locations and line amplification at intermediate locations to mitigate fiber loss. 100Gbps IPoDWDM interface should support OTN G.709 with QPSK and 8-QAM modulation. Should have C-band wavelength output with 50GHz spacing with support of FEC and SD-FEC for extended reach should be able to upgrade to 200Gbps on same hardware and fiber with future scalable license only.
The proposed system shall be a 100G optimized transport system based on 100G coherent technology. As such, vendor's proposal shall eliminate use of existing system's Dispersion Compensation Modules (DCMs), yet use a design strategy that supports transmission of 10G optical wavelengths that coexist with 100G (and higher) coherent wavelengths across same span. Describe strategy used to avoid interference between 10G wavelengths and 100G wavelengths, such as grouping like wavelengths in separate groups of channels and possible use of guard channels between groups.
The proposed system shall have ROADMs at all add/drop nodes. ROADMs shall support mapping of pass-through wavelengths via remote access to node or through network management system without requiring patch cables for individual pass-through wavelengths
Each network element in proposed system must support at least one physical interface through which a technician can attach a computer to perform configuration, management, and other functions. Each network element must support an RJ-45 (10Base-T, 100Base-T, or 1000Base-T) Ethernet interface, and optionally an RS-232 serial interface.
Local configuration and management must be provided by an intuitive Command Line Interface (CLI) or graphical craft interface (via an Element Management System or Web User Interface). Describe features for connecting to and locally configuring and managing a network element. Describe ability to remotely access other network elements in proposed system through local interface on network element.
The proposed system must have a Network Management System (NMS), a software package that provides full-featured and simultaneous network management for all network elements in network. Describe structure of proposed NMS, underlying operating system and server required to support it for proposed network and anticipated growth, server/client architecture whereby multiple clients can access it simultaneously, and projected limits on number of network elements system can manage and number of clients that can access server simultaneously.
Network solution offered by bidders shall support minimum but not limited to following functions of overall network.
The offered solution shall have capability to be controlled and managed through a central location having concept of centralized control and decentralized execution
AAA function is performed for every subscriber from this central site. In addition, management functions like fault, configuration, accounting, performance, and security management of every network component should be done centrally.
Network equipment should be IPv6 ready and shall be capable of operating on IPv4 as well as IPv6. All features of IPv4 and IPv6 shall be available.
Equipment should be supplied with cabinets with perforated walls/doors in accordance with associated ETSI
Bidders shall also guarantee compatibility of offered equipment with industry leading network/equipment.
Offered equipment shall conform to latest relevant International Standards and Recommendations such as International Standards Organization (ISO), International Telecommunications Union – Telecommunications Sector (ITU-T), Internet Engineering Task Force (IETF), Institute of Electrical and Electronics Engineers (IEEE), and other recognized standard bodies.
Bidders shall submit system functional block diagram of all main units of offered systems indicating function of each block.
Bidders shall not quote end of sale and end of life products/services. Equipment should be in support services for at least ten (10) years. Bidders shall also provide life cycle of offered equipment of ten
The requirements specified herein are minimum requirements and can always be exceeded. In this regard, bidders shall enumerate and describe additional capabilities/features of offered system. Failure of doing so shall allow SCO to do free interpretation of responded information.
Offered equipment manufacturer should be NIAP Certification (National Information Assurance Partnership)
Offered equipment shall be scalable. Bidders shall provide ultimate capacity of offered system.
All devices selected should be ready to support all features required from day one to run services defined in this document
Bidders are encouraged to quote complete solution. However, SCO reserves right to split order for different network components
Bidders are required to offer preferably modular operating system.
Bidders shall provide a proven record of delivering services enumerated in this document with offered network elements in various installations around the world.
Offered system shall be a proven one and must have been supplied in sufficient quantity and in operation in various countries for last one year as a minimum.
7.22 Bidders shall be compliant to telecommunication industry TL9000 quality standard. TL 9000 defines quality system requirements for design, development, production, delivery, installation, and maintenance of product and services.
7.23 Bidders shall work jointly with SCO to exchange field performance information based on TL9000 requirements, help SCO understand and use performance metrics to categorize supplied product. Furthermore, bidders shall provide quarterly quality report based on TL9000 measurements specific to supplied offered equipment systems. Exchange of information between bidders and SCO should be treated as strictly confidential and must not be disclosed to any third party without SCO's prior approval. Any lapses in this regard would be treated as per Non-Disclosure Agreement.
All offered equipment shall operate without degradation of performance within following ranges
Ambient Temperature: 0°C to +55°C (Partial compliance would not be acceptable. Therefore, this is either Complied or NOT Complied)
Relative Humidity: up to 95%, non-condensing
Bidders shall provide details of physical dimensioning of system offered to SCO as following:
a. Height
b. Width
c. Depth
d. Weight
All offered equipment and installation materials required for complete installation shall be packed separately for each base and shall be shipped in such a way that they can be delivered to site together. Packing used shall be good enough to prevent entry of dust during shipping and storage.
A "Warning Label" should identify offered equipment/plug-in units containing electrostatic sensitive devices (ESD).
Packaging of ESD should offer protection against static electricity, e.g., use of static shielding bags/material.
Shelves accommodating plug-in units shall effectively be sealed from dust entry.
Bidders shall observe appropriate safety regulations for protection of personnel.
In design of offered equipment, special emphasis shall be placed on safeguarding personnel from high voltages. Any voltage of 60V or more is considered hazardous.
Outside of all cabinets and cabinets and chassis of all enclosed components, and all external parts, meter cases, control shafts, and knobs shall be securely earthed.
For units having laser source, special notice to be given to draw attention of operating staff to avoid close proximity to such units.
All conductors and components must be chosen with regard to reducing fire risks. Inflammable materials should be avoided.
Bidders will ensure that offered equipment supplied is of latest model and no part is refurbished.
Bidders shall not quote end of sale and end of life products/services.
Bidders shall also provide life cycle of offered network components.
SCO reserves right to add (on quoted price) or drop any offered equipment in order placed on successful bidder.
SCO reserves right to make changes in specification at any time without any notice.
SCO cannot guarantee that any of requirements, standards, regulations, and conditions of this specification are not covered or protected by copyright or patent of a third party.
Bidders shall provide roadmap of all offered products/components of offered solution.
All requirements mentioned in this specification shall be met with nonproprietary internationally recognized open standards. Protocol and interface standards used (including version/date) must be provided for each item.
Proposed equipment must support IPv6/IPv4 as their native protocol. Supplied hardware should be IPv6/IPv4 ready.
All proposed equipment shall be centrally configurable, monitorable, and manageable.
Bidders shall provide following equipment details where needed
Chassis configuration
Chassis extension/Number of chassis per rack if applicable
Common equipment such as power, fan, etc.
Number of slot per chassis of equipment considering full redundancy
Number of ports per slot per interface type
Capacity per port per slot
Capacity per slot
Type of line cards available/supported
Number of slots dedicated for line cards and whether they are passive or active
Equipment Model
Year of manufacture
Equipment life cycle and future roadmap
Bidders should highlight any limitation with reference to proposed chassis/slot.
Interoperability of offered solution with existing and planned SCO network shall be ensured.
Integration of offered equipment with existing SCO network is responsibility of IP core bidder. Similarly, integration of all newly installed systems/NEs is also responsibility of IP core vendor. Other vendors are instructed to use standard interfaces; however, IP core will act as host to provide interconnectivity on IP domain.
Vendor selected/awarded contract for IP core will be overall responsible for integration of entire project that includes interconnectivity with other vendors' equipment (including but not limited to) OLT, ONT, STB, IPTV, routers, switches, media gateways, SDH, DWDM, OTN, soft switch, CATV headend, billing, and BRAS. These systems include already installed network elements as well network elements planned in other domains of this project.
System shall support hot upgrade without service interruption. In case of service interruption, vendor should specify downtime. Bidder/selected vendor should submit test reports to support their claim.
备件管理
解决方案提供商应保证从设备调试日期起最少十（10）年内备件组件、单元、子单元和兼容设备的可用性。
5%的备件必须是TCO的一部分。对于每个关键备件，即转发器/复用器或任何其他影响服务的板卡应可用，每个定义的区域维护中心按区域提供（每种类型至少提供7个备用板卡，将放置在7个指定区域并由解决方案提供商维护）。
迁移计划
进行迁移的程序方法
与提出的PIP一致的维护窗口（MW）要求
路线图
至少10年的软件和硬件路线图，包含EOS详细信息
详细的产品描述和数据表
物料清单
站点级详细BoM，涵盖硬件、软件、服务、培训和本地材料等（应在商业报价中复制为BoQ）
正确填写的BOQ-TCO格式
运营和维护提案
成功案例
包含运营商名称、产品名称、调试年份、网络类型、保护方案和联系详细信息的成功案例
项目实施计划
详细的PIP
培训提案
国外：2批次8人
本地：伊斯兰堡和卡拉奇各15人，分2批次
提出的设备应支持用于服务汇聚的电交叉连接架构
提出的设备应支持光层的ROADM架构
提出的设备应支持最少每通道100G容量
光层应设计为96CH以确保未来扩展能力
提出的网络应为无DCM、相干架构
提出的设备应支持电ASON
提出的设备应支持WSON
投标方必须明确指定单个ASON域的总NE（网络元素）数量
提出的设备应支持超过100G带宽容量，即400G单载波用于未来升级
提出的设备应支持具有集中能力的通用交叉连接板，以支持SDH、PKT和OTN汇聚
提出的设备应支持可编程线路板
交叉连接板应支持VC级汇聚
提出设备的每槽容量不应少于400G，应可升级到1T而无需更换机箱
提出的设备，100G单线路板应支持SDH、PKT和OTN功能。
提出的设备应支持客户端和线路侧服务板的L2 PKT功能（MPLS-TP）和OTN功能
提出的设备应支持无色、无方向、无竞争和无网格（CDCG）ROADM。但是对于初始范围，可以提出无色和无方向ROADM，它能够以最少或无硬件更改升级到无竞争和无网格
提出的设备应具有内置OTDR和OSNR监控功能
端到端服务延迟测量功能应在NMS功能中可用
供应商应提供4个客户端终端，初始许可证为10个客户端，每个都具有多个基于登录的功能
提出的设备应支持SDN，无需任何硬件更改用于未来升级
提出的设备从购买日期起应具有最少10年的生命周期用于网络扩展（可购买），支持结束不应少于15年（备件可用性）
提出的网络应设计为在提供的每个链路损耗基础上增加3dB每链路损耗余量。
提出的网络应配置为从提出线路板的最小阈值增加最少4dB OSNR余量。
应提供提出线路板的最小OSNR容差、CD和PMD容差以及解决方案。
提出的解决方案应在所有ADD/DROP位置和环交叉站点引用电交叉连接子机架，如提供的拓扑中定义。
提出的交叉连接板应具有用于SDH、PKT和OTN服务汇聚的集中矩阵。
交叉连接板应提供完整容量许可证。
在配置当前解决方案所需板卡后，提出的电子机架应有足够的空闲槽位以支持至少2x 100G更多lambda添加，包括客户端和线路板。
应提出具有GE和10GE光接口的L2支持PKT客户板，最好在单板中
应提出支持从ODU0~ODU1~ODU2透明GE、10GE和STM-N多速率接口的OTN客户板。
提出的100G线路板应支持SDH、PKT和OTN服务。
提出的解决方案应具有MPLS-TP功能以支持PKT服务要求。
提出的解决方案应支持电ASON用于较小粒度服务，即FE、GE、10GE和STM-N，用于5个上下路城市之间的100G共享容量lambda，如流量矩阵中提供
提出的解决方案应设计为从第一天起具有WSON交换能力，用于主要城市之间的点对点快速lambda，即伊斯兰堡、拉合尔和卡拉奇之间。
投标方应明确说明提出的ASON SLA。
所有ADD/DROP位置，包括所有环交叉节点（具有超过2个方向），应配置为具有最少2个本地维度的无方向ROADM。具有2个方向的ADD/DROP位置可以配置为有方向ROADM。
对于100G共享容量lambda，服务保护应设计为能够保护和重路由环级流量，如提供的拓扑中定义。
流量应防止多个光纤切断并应重路由到任何可用路径。保护可以通过使用ASON（首选）或任何PKT保护方案实现第3度保护（对于PKT服务）。
服务切换时间应少于50ms。投标方需要明确提及ASON服务的重路由时间。
提出的解决方案应支持在线服务OTDR功能。
提出的解决方案应支持在线OSNR监控功能
需要提供详细的HLD、提出的设备文档和站点级典型和最大功率要求以及BOQ。
应为每个阶段单独提供10%备件。每个区域即北部、中部和南部的关键板卡数量不应少于2个。这里的关键板卡包括但不限于交叉连接板、客户板、线路板、WSS、放大器和上下路组件或任何故障可能导致服务中断的项目。每个区域应提供最少1个子机架电和光作为备件。
在流量矩阵中定义为规划池的BOQ需要提供所有必需的许可证。
请参阅第4节中的说明，如果有任何说明未满足，请回复
请参考SOW内容
请参考SOW内容
请参考SOW内容
请参考SOW内容
请参考SOW内容
请参考SOW内容
投标方应为Wateen技术人员提供以下国外培训
网络规划/配置和优化：2人
运营和维护：6人
投标方应为Wateen员工提供12人的本地运维培训
此外，投标方应提供产品的所有技术文档、线路/配置和故障排除手册。
投标方应负责完整的端到端登机、住宿和其他培训相关费用。
投标方应负责提供三（3）年FOC保修，包括R&R。保修将从PAC发布日期开始
供应商应承担免费软件错误修复、软件/补丁升级，以解决网络中出现的任何软件/硬件不兼容问题。
投标方不应对任何设备/网络/NMS软件升级收取费用，包括主软件/独特功能升级的年度许可证费用。
任何未来的设备/网络软件/功能升级都应免费承担。
任何未来的NMS软件升级都应免费承担
投标方应为上述每个阶段的服务迁移成本单独报价，从现有网络（3 *10G SDH/DWDM）
投标方应在网络中出现突出问题时提供二级和三级技术支持
提出的设备从购买日期起应具有最少10年的生命周期用于网络扩展（可购买），支持结束不应少于15年（备件可用性）。
BOQ数量的10%应作为运维目的的备件和故障情况下冗余硬件的可用性。这些备件与流量矩阵中描述的规划池分开。关键板卡包括但不限于交叉连接板、客户板、线路板、WSS、放大器和上下路组件或任何故障可能导致服务中断的项目。每个区域应提供最少1个子机架电和光作为备件。
技术报价和商业报价应分别提交。
商业报价应与技术报价保持一致。技术和商业报价都需要明确的分阶段提交。
不属于解决方案但以后可能需要的STM-N TDM板、100G客户板、可编程线路板和相关许可证应在可选项目列表中提供。
应报价KHI-ISB、KHI-LHR和LHR-ISB的快速Lambda解决方案以及每lambda价格。
应报价单位站点添加成本，以及所需的BOQ，包括但不限于电子机架、线路和客户板、辅助板和相关许可证。1+1客户板以及2 x 100G线路板可作为参考报价的标准配置。
从OLA到ROADM（2方向）的站点转换成本以及BOQ，包括额外放大器、WSS、MUX/DEMUX、光子机架和相关许可证。
服务成本应与设备一起考虑。
包括硬件和许可证在内的任何项目，如果是报价解决方案完整功能所需的，如果未捕获将是投标方的责任，应免费提供。
Wateen可以根据要求分阶段或整体订购BOQ。但是供应商需要为包括WSON和Ring#9在内的所有阶段提供报价。所有范围和UPL的价格将与成功投标方锁定。
VLAN交换
MAC/中继共享（点对多点）
桥接（以太网LAN）
LAG
DLAG/ MC-LAG
跨板LAG支持（2个L2客户板之间的LAG
板间LAG节点平衡
LPT
实时监控（数据包输入/输出，与帧/冲突/丢弃事件相关的错误）等
VLAN嵌套/堆叠（QinQ 802.1ad）
MPLS功能
QoS策略/流量整形（CAR/CoS/ H-QoS）
实时利用率（图表和延迟监控）
伪线隧道
基准测试套件（RFC2544等）
L2以太网交换机/服务板支持的最大中继端口数
100mb以下的带宽限制。最低可能的带宽限制
MS-PW+隧道APS
PW APS+隧道APS
双归属保护
OAM
未来SDN，支持按需带宽等功能
在线服务调整
